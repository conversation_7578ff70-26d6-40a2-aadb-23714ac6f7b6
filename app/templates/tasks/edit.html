{% extends "base.html" %}

{% block title %}编辑任务{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>编辑任务: {{ task.name }}</h2>
                <a href="{{ url_for('tasks.list_tasks') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>返回任务列表
                </a>
            </div>

            <div class="card">
                <div class="card-body">
                    <form method="POST" id="taskForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">任务名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" value="{{ task.name }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="monitor_platform" class="form-label">监控平台 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="monitor_platform" name="monitor_platform" required>
                                        <option value="">请选择平台</option>
                                        <option value="tiktok" {% if task.monitor_platform == 'tiktok' %}selected{% endif %}>TikTok</option>
                                        <option value="douyin" {% if task.monitor_platform == 'douyin' %}selected{% endif %}>抖音</option>
                                        <option value="bilibili" {% if task.monitor_platform == 'bilibili' %}selected{% endif %}>哔哩哔哩</option>
                                        <option value="xhs" {% if task.monitor_platform == 'xhs' %}selected{% endif %}>小红书</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="monitor_account_id" class="form-label">监控账号 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="monitor_account_id" name="monitor_account_id" required>
                                        <option value="">请选择监控账号</option>
                                        {% for account in monitor_accounts %}
                                        <option value="{{ account.id }}" {% if account.id == task.monitor_account_id %}selected{% endif %}>
                                            {{ account.name }} ({{ account.platform }})
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="upload_account_id" class="form-label">上传账号</label>
                                    <select class="form-select" id="upload_account_id" name="upload_account_id">
                                        <option value="">请选择上传账号（可选）</option>
                                        {% for account in upload_accounts %}
                                        <option value="{{ account.id }}" {% if account.id == task.upload_account_id %}selected{% endif %}>
                                            {{ account.name }} ({{ account.platform }})
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">任务描述</label>
                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="请输入任务描述...">{{ task.description or '' }}</textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="monitor_interval" class="form-label">监控间隔（秒）</label>
                                    <input type="number" class="form-control" id="monitor_interval" name="monitor_interval" 
                                           value="{{ task.monitor_interval or 300 }}" min="60">
                                    <div class="form-text">建议不少于60秒，避免频繁请求</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="max_videos" class="form-label">最大视频数量</label>
                                    <input type="number" class="form-control" id="max_videos" name="max_videos" 
                                           value="{{ task.max_videos or 10 }}" min="1">
                                    <div class="form-text">每次监控获取的最大视频数量</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       {% if task.is_active %}checked{% endif %}>
                                <label class="form-check-label" for="is_active">
                                    启用任务
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">监控配置</label>
                            <div class="card">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="target_user_id" class="form-label">目标用户ID <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" id="target_user_id" name="target_user_id" 
                                                       value="{{ task.monitor_config.target_user_id if task.monitor_config else '' }}" required>
                                                <div class="form-text">要监控的用户ID或用户名</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="keywords" class="form-label">关键词过滤</label>
                                                <input type="text" class="form-control" id="keywords" name="keywords" 
                                                       value="{% if task.monitor_config and task.monitor_config.keywords %}{{ task.monitor_config.keywords|join(',') }}{% endif %}"
                                                       placeholder="用逗号分隔多个关键词">
                                                <div class="form-text">只下载包含这些关键词的视频（可选）</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">处理配置</label>
                            <div class="card">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="enable_deduplication" name="enable_deduplication"
                                                       {% if task.process_config and task.process_config.enable_deduplication %}checked{% endif %}>
                                                <label class="form-check-label" for="enable_deduplication">
                                                    启用去重
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="enable_pseudo_original" name="enable_pseudo_original"
                                                       {% if task.process_config and task.process_config.enable_pseudo_original %}checked{% endif %}>
                                                <label class="form-check-label" for="enable_pseudo_original">
                                                    启用伪原创
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="auto_upload" name="auto_upload"
                                                       {% if task.auto_upload %}checked{% endif %}>
                                                <label class="form-check-label" for="auto_upload">
                                                    自动上传
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ url_for('tasks.list_tasks') }}" class="btn btn-secondary">取消</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>保存更改
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('taskForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = {};
    
    // 收集表单数据
    for (let [key, value] of formData.entries()) {
        if (key === 'is_active' || key === 'enable_deduplication' || 
            key === 'enable_pseudo_original' || key === 'auto_upload') {
            data[key] = true;
        } else {
            data[key] = value;
        }
    }
    
    // 处理未选中的复选框
    ['is_active', 'enable_deduplication', 'enable_pseudo_original', 'auto_upload'].forEach(field => {
        if (!formData.has(field)) {
            data[field] = false;
        }
    });
    
    // 构建监控配置
    data.monitor_config = {
        target_user_id: data.target_user_id,
        keywords: data.keywords ? data.keywords.split(',').map(k => k.trim()) : []
    };
    
    // 构建处理配置
    data.process_config = {
        enable_deduplication: data.enable_deduplication,
        enable_pseudo_original: data.enable_pseudo_original
    };
    
    // 删除已经包含在配置中的字段
    delete data.target_user_id;
    delete data.keywords;
    delete data.enable_deduplication;
    delete data.enable_pseudo_original;
    
    // 提交表单
    fetch(this.action, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = "{{ url_for('tasks.list_tasks') }}";
        } else {
            alert('保存失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('保存失败，请重试');
    });
});
</script>
{% endblock %}
