{% extends "base.html" %}

{% block title %}任务详情{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 页面标题 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">任务详情</h1>
                    <p class="text-muted">查看和管理任务信息</p>
                </div>
                <div>
                    <a href="{{ url_for('tasks.edit_task', task_id=task.id) }}" class="btn btn-primary">
                        <i class="fas fa-edit"></i> 编辑任务
                    </a>
                    <a href="{{ url_for('tasks.list_tasks') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> 返回列表
                    </a>
                </div>
            </div>

            <!-- 简单的任务信息 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">任务信息</h5>
                </div>
                <div class="card-body">
                    <p><strong>任务名称:</strong> {{ task.name or '未知' }}</p>
                    <p><strong>任务描述:</strong> {{ task.description or '无' }}</p>
                    <p><strong>监控平台:</strong> {{ task.monitor_platform or '未设置' }}</p>
                    <p><strong>上传平台:</strong> {{ task.upload_platform or '未设置' }}</p>
                    <p><strong>任务状态:</strong>
                        {% if task.is_active %}
                            <span class="badge bg-success">活跃</span>
                        {% else %}
                            <span class="badge bg-secondary">暂停</span>
                        {% endif %}
                    </p>
                    <p><strong>创建时间:</strong> {{ task.created_at.strftime('%Y-%m-%d %H:%M:%S') if task.created_at else '未知' }}</p>
                </div>
            </div>

        </div>
    </div>
</div>
{% endblock %}
