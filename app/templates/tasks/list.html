{% extends "base.html" %}

{% block title %}任务管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">任务管理</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="{{ url_for('tasks.create_task') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-plus me-1"></i>新建任务
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- 过滤器 -->
            <div class="card mb-3">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="status" class="form-label">状态</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">全部状态</option>
                                <option value="running" {% if status_filter == 'running' %}selected{% endif %}>运行中</option>
                                <option value="stopped" {% if status_filter == 'stopped' %}selected{% endif %}>已停止</option>
                                <option value="error" {% if status_filter == 'error' %}selected{% endif %}>错误</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="platform" class="form-label">平台</label>
                            <select class="form-select" id="platform" name="platform">
                                <option value="">全部平台</option>
                                <option value="tiktok" {% if platform_filter == 'tiktok' %}selected{% endif %}>TikTok</option>
                                <option value="douyin" {% if platform_filter == 'douyin' %}selected{% endif %}>抖音</option>
                                <option value="bilibili" {% if platform_filter == 'bilibili' %}selected{% endif %}>哔哩哔哩</option>
                                <option value="xhs" {% if platform_filter == 'xhs' %}selected{% endif %}>小红书</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="per_page" class="form-label">每页显示</label>
                            <select class="form-select" id="per_page" name="per_page">
                                <option value="10" {% if per_page == 10 %}selected{% endif %}>10</option>
                                <option value="20" {% if per_page == 20 %}selected{% endif %}>20</option>
                                <option value="50" {% if per_page == 50 %}selected{% endif %}>50</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>筛选
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- 任务列表 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">任务列表 (共 {{ pagination.total }} 个)</h5>
                </div>
                <div class="card-body p-0">
                    {% if tasks %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>任务名称</th>
                                    <th>监控平台</th>
                                    <th>上传平台</th>
                                    <th>状态</th>
                                    <th>最后运行</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for task in tasks %}
                                <tr>
                                    <td>{{ task.id }}</td>
                                    <td>
                                        <strong>{{ task.name }}</strong>
                                        {% if task.description %}
                                        <br><small class="text-muted">{{ task.description[:50] }}{% if task.description|length > 50 %}...{% endif %}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ task.monitor_platform }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">{{ task.upload_platform }}</span>
                                    </td>
                                    <td>
                                        {% if task.status == 'running' %}
                                        <span class="badge bg-success">运行中</span>
                                        {% elif task.status == 'stopped' %}
                                        <span class="badge bg-secondary">已停止</span>
                                        {% elif task.status == 'error' %}
                                        <span class="badge bg-danger">错误</span>
                                        {% else %}
                                        <span class="badge bg-light text-dark">{{ task.status }}</span>
                                        {% endif %}
                                        
                                        {% if not task.is_active %}
                                        <span class="badge bg-warning">已禁用</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if task.last_run %}
                                        {{ task.last_run.strftime('%Y-%m-%d %H:%M') }}
                                        {% else %}
                                        <span class="text-muted">从未运行</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{{ url_for('tasks.view_task', task_id=task.id) }}" 
                                               class="btn btn-outline-info" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('tasks.edit_task', task_id=task.id) }}" 
                                               class="btn btn-outline-primary" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% if task.status == 'running' %}
                                            <button class="btn btn-outline-warning" 
                                                    onclick="controlTask({{ task.id }}, 'stop')" title="停止">
                                                <i class="fas fa-stop"></i>
                                            </button>
                                            {% else %}
                                            <button class="btn btn-outline-success" 
                                                    onclick="controlTask({{ task.id }}, 'start')" title="启动">
                                                <i class="fas fa-play"></i>
                                            </button>
                                            {% endif %}
                                            <button class="btn btn-outline-danger" 
                                                    onclick="deleteTask({{ task.id }})" title="删除">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无任务数据</p>
                        <a href="{{ url_for('tasks.create_task') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>创建第一个任务
                        </a>
                    </div>
                    {% endif %}
                </div>
                
                {% if pagination.pages > 1 %}
                <div class="card-footer">
                    <nav aria-label="任务分页">
                        <ul class="pagination justify-content-center mb-0">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('tasks.list_tasks', page=pagination.prev_num, status=status_filter, platform=platform_filter, per_page=per_page) }}">上一页</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in pagination.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('tasks.list_tasks', page=page_num, status=status_filter, platform=platform_filter, per_page=per_page) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('tasks.list_tasks', page=pagination.next_num, status=status_filter, platform=platform_filter, per_page=per_page) }}">下一页</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
function controlTask(taskId, action) {
    if (confirm(`确定要${action === 'start' ? '启动' : '停止'}这个任务吗？`)) {
        fetch(`/tasks/${taskId}/${action}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('操作失败: ' + data.message);
            }
        })
        .catch(error => {
            alert('操作失败: ' + error);
        });
    }
}

function deleteTask(taskId) {
    if (confirm('确定要删除这个任务吗？此操作不可恢复！')) {
        fetch(`/tasks/${taskId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('删除失败: ' + data.message);
            }
        })
        .catch(error => {
            alert('删除失败: ' + error);
        });
    }
}
</script>
{% endblock %}
