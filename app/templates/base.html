<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}视频监控管理系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
        }
        .sidebar .nav-link {
            color: #adb5bd;
        }
        .sidebar .nav-link:hover {
            color: #fff;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #495057;
        }
        .main-content {
            margin-left: 0;
        }
        @media (min-width: 768px) {
            .main-content {
                margin-left: 250px;
            }
        }
        .status-badge {
            font-size: 0.75rem;
        }
        .card-stats {
            border-left: 4px solid;
        }
        .card-stats.primary {
            border-left-color: #007bff;
        }
        .card-stats.success {
            border-left-color: #28a745;
        }
        .card-stats.warning {
            border-left-color: #ffc107;
        }
        .card-stats.danger {
            border-left-color: #dc3545;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar d-md-block bg-dark position-fixed" style="width: 250px; z-index: 1000;">
        <div class="position-sticky pt-3">
            <div class="px-3 mb-3">
                <h5 class="text-white">视频监控系统</h5>
            </div>
            
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint == 'main.index' %}active{% endif %}"
                       href="{{ url_for('main.index') }}">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        首页
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint and 'tasks' in request.endpoint %}active{% endif %}" 
                       href="{{ url_for('tasks.list_tasks') }}">
                        <i class="fas fa-tasks me-2"></i>
                        任务管理
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint and 'accounts' in request.endpoint %}active{% endif %}" 
                       href="{{ url_for('accounts.list_accounts') }}">
                        <i class="fas fa-users me-2"></i>
                        账号管理
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint and 'videos' in request.endpoint %}active{% endif %}" 
                       href="{{ url_for('videos.list_videos') }}">
                        <i class="fas fa-video me-2"></i>
                        视频管理
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" data-bs-toggle="collapse" data-bs-target="#systemMenu">
                        <i class="fas fa-cog me-2"></i>
                        系统管理
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse" id="systemMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link" href="#">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    系统监控
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#">
                                    <i class="fas fa-file-alt me-2"></i>
                                    日志查看
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#">
                                    <i class="fas fa-wrench me-2"></i>
                                    系统设置
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>
            </ul>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="main-content">
        <!-- 顶部导航栏 -->
        <nav class="navbar navbar-expand-lg navbar-light bg-light border-bottom">
            <div class="container-fluid">
                <button class="navbar-toggler d-md-none" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            管理员
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>个人资料</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <!-- 页面内容 -->
        <div class="container-fluid py-4">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            {% block content %}{% endblock %}
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        // 全局JavaScript函数
        function showLoading(element) {
            if (element) {
                element.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>加载中...';
                element.disabled = true;
            }
        }
        
        function hideLoading(element, originalText) {
            if (element) {
                element.innerHTML = originalText;
                element.disabled = false;
            }
        }
        
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.container-fluid');
            container.insertBefore(alertDiv, container.firstChild);
            
            // 自动消失
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
        
        function formatDateTime(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 状态徽章样式映射
        const statusBadgeMap = {
            'pending': 'secondary',
            'running': 'primary',
            'completed': 'success',
            'failed': 'danger',
            'error': 'danger',
            'paused': 'warning',
            'stopped': 'secondary',
            'active': 'success',
            'inactive': 'secondary',
            'uploading': 'info',
            'processing': 'info',
            'downloading': 'info'
        };
        
        function getStatusBadge(status) {
            const badgeClass = statusBadgeMap[status] || 'secondary';
            return `<span class="badge bg-${badgeClass} status-badge">${status}</span>`;
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
