{% extends "base.html" %}

{% block title %}账号管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">账号管理</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="{{ url_for('accounts.create_account') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-plus me-1"></i>新建账号
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- 过滤器 -->
            <div class="card mb-3">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="platform" class="form-label">平台</label>
                            <select class="form-select" id="platform" name="platform">
                                <option value="">全部平台</option>
                                <option value="tiktok" {% if platform_filter == 'tiktok' %}selected{% endif %}>TikTok</option>
                                <option value="douyin" {% if platform_filter == 'douyin' %}selected{% endif %}>抖音</option>
                                <option value="bilibili" {% if platform_filter == 'bilibili' %}selected{% endif %}>哔哩哔哩</option>
                                <option value="xhs" {% if platform_filter == 'xhs' %}selected{% endif %}>小红书</option>
                                <option value="youtube" {% if platform_filter == 'youtube' %}selected{% endif %}>YouTube</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="type" class="form-label">类型</label>
                            <select class="form-select" id="type" name="type">
                                <option value="">全部类型</option>
                                <option value="monitor" {% if type_filter == 'monitor' %}selected{% endif %}>监控账号</option>
                                <option value="upload" {% if type_filter == 'upload' %}selected{% endif %}>上传账号</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">状态</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">全部状态</option>
                                <option value="active" {% if status_filter == 'active' %}selected{% endif %}>正常</option>
                                <option value="expired" {% if status_filter == 'expired' %}selected{% endif %}>已过期</option>
                                <option value="error" {% if status_filter == 'error' %}selected{% endif %}>错误</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>筛选
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- 账号列表 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">账号列表 (共 {{ pagination.total }} 个)</h5>
                </div>
                <div class="card-body p-0">
                    {% if accounts %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>用户名</th>
                                    <th>平台</th>
                                    <th>类型</th>
                                    <th>状态</th>
                                    <th>最后检查</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for account in accounts %}
                                <tr>
                                    <td>{{ account.id }}</td>
                                    <td>
                                        <strong>{{ account.username }}</strong>
                                        {% if account.display_name %}
                                        <br><small class="text-muted">{{ account.display_name }}</small>
                                        {% endif %}
                                        {% if account.user_id %}
                                        <br><small class="text-info">ID: {{ account.user_id }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if account.platform == 'tiktok' %}
                                        <span class="badge bg-dark">TikTok</span>
                                        {% elif account.platform == 'douyin' %}
                                        <span class="badge bg-primary">抖音</span>
                                        {% elif account.platform == 'bilibili' %}
                                        <span class="badge bg-info">哔哩哔哩</span>
                                        {% elif account.platform == 'xhs' %}
                                        <span class="badge bg-danger">小红书</span>
                                        {% elif account.platform == 'youtube' %}
                                        <span class="badge bg-warning">YouTube</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ account.platform }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if account.account_type == 'monitor' %}
                                        <span class="badge bg-success">监控账号</span>
                                        {% else %}
                                        <span class="badge bg-primary">上传账号</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if account.status == 'active' %}
                                        <span class="badge bg-success">正常</span>
                                        {% elif account.status == 'expired' %}
                                        <span class="badge bg-warning">已过期</span>
                                        {% elif account.status == 'error' %}
                                        <span class="badge bg-danger">错误</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ account.status }}</span>
                                        {% endif %}
                                        
                                        {% if not account.is_active %}
                                        <span class="badge bg-light text-dark">已禁用</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if account.last_check %}
                                        {{ account.last_check.strftime('%Y-%m-%d %H:%M') }}
                                        {% else %}
                                        <span class="text-muted">从未检查</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{{ url_for('accounts.view_account', account_id=account.id) }}" 
                                               class="btn btn-outline-info" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('accounts.edit_account', account_id=account.id) }}" 
                                               class="btn btn-outline-primary" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-outline-success" 
                                                    onclick="checkAccount({{ account.id }})" title="检查状态">
                                                <i class="fas fa-sync"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" 
                                                    onclick="deleteAccount({{ account.id }})" title="删除">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-user-cog fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无账号数据</p>
                        <a href="{{ url_for('accounts.create_account') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>添加第一个账号
                        </a>
                    </div>
                    {% endif %}
                </div>
                
                {% if pagination.pages > 1 %}
                <div class="card-footer">
                    <nav aria-label="账号分页">
                        <ul class="pagination justify-content-center mb-0">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('accounts.list_accounts', page=pagination.prev_num, platform=platform_filter, type=type_filter, status=status_filter, per_page=per_page) }}">上一页</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in pagination.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('accounts.list_accounts', page=page_num, platform=platform_filter, type=type_filter, status=status_filter, per_page=per_page) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('accounts.list_accounts', page=pagination.next_num, platform=platform_filter, type=type_filter, status=status_filter, per_page=per_page) }}">下一页</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
function checkAccount(accountId) {
    if (confirm('确定要检查这个账号的状态吗？')) {
        fetch(`/accounts/${accountId}/check`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('检查完成: ' + data.message);
                location.reload();
            } else {
                alert('检查失败: ' + data.message);
            }
        })
        .catch(error => {
            alert('检查失败: ' + error);
        });
    }
}

function deleteAccount(accountId) {
    if (confirm('确定要删除这个账号吗？此操作不可恢复！')) {
        fetch(`/accounts/${accountId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('删除失败: ' + data.message);
            }
        })
        .catch(error => {
            alert('删除失败: ' + error);
        });
    }
}
</script>
{% endblock %}
