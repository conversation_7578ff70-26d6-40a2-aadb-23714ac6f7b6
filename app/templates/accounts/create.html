{% extends "base.html" %}

{% block title %}创建账号{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>创建新账号</h2>
                <a href="{{ url_for('accounts.list_accounts') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>返回账号列表
                </a>
            </div>

            <div class="card">
                <div class="card-body">
                    <form method="POST" id="accountForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">账号名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                    <div class="form-text">用于识别账号的名称</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="platform" class="form-label">平台 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="platform" name="platform" required>
                                        <option value="">请选择平台</option>
                                        <option value="tiktok">TikTok</option>
                                        <option value="douyin">抖音</option>
                                        <option value="bilibili">哔哩哔哩</option>
                                        <option value="xhs">小红书</option>
                                        <option value="youtube">YouTube</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="account_type" class="form-label">账号类型 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="account_type" name="account_type" required>
                                        <option value="">请选择类型</option>
                                        <option value="monitor">监控账号</option>
                                        <option value="upload">上传账号</option>
                                    </select>
                                    <div class="form-text">监控账号用于获取视频，上传账号用于发布视频</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="username" class="form-label">用户名</label>
                                    <input type="text" class="form-control" id="username" name="username">
                                    <div class="form-text">平台上的用户名或ID</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">账号描述</label>
                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="请输入账号描述..."></textarea>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                <label class="form-check-label" for="is_active">
                                    启用账号
                                </label>
                            </div>
                        </div>

                        <!-- 监控账号配置 -->
                        <div id="monitor_config" class="mb-3" style="display: none;">
                            <label class="form-label">监控配置</label>
                            <div class="card">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="user_id" class="form-label">目标用户ID <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" id="user_id" name="user_id">
                                                <div class="form-text">要监控的用户ID</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="proxy" class="form-label">代理设置</label>
                                                <input type="text" class="form-control" id="proxy" name="proxy" placeholder="http://proxy:port">
                                                <div class="form-text">可选的代理服务器</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 上传账号配置 -->
                        <div id="upload_config" class="mb-3" style="display: none;">
                            <label class="form-label">上传配置</label>
                            <div class="card">
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="cookies" class="form-label">Cookies <span class="text-danger">*</span></label>
                                        <textarea class="form-control" id="cookies" name="cookies" rows="5" 
                                                  placeholder="请粘贴从浏览器复制的cookies..."></textarea>
                                        <div class="form-text">从浏览器开发者工具中复制的完整cookies</div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="upload_interval" class="form-label">上传间隔（秒）</label>
                                                <input type="number" class="form-control" id="upload_interval" name="upload_interval" value="3600" min="300">
                                                <div class="form-text">两次上传之间的间隔时间</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="max_daily_uploads" class="form-label">每日最大上传数</label>
                                                <input type="number" class="form-control" id="max_daily_uploads" name="max_daily_uploads" value="10" min="1">
                                                <div class="form-text">每天最多上传的视频数量</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ url_for('accounts.list_accounts') }}" class="btn btn-secondary">取消</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>创建账号
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 账号类型变化时显示/隐藏相应配置
document.getElementById('account_type').addEventListener('change', function() {
    const type = this.value;
    const monitorConfig = document.getElementById('monitor_config');
    const uploadConfig = document.getElementById('upload_config');
    
    if (type === 'monitor') {
        monitorConfig.style.display = 'block';
        uploadConfig.style.display = 'none';
        // 设置必填字段
        document.getElementById('user_id').required = true;
        document.getElementById('cookies').required = false;
    } else if (type === 'upload') {
        monitorConfig.style.display = 'none';
        uploadConfig.style.display = 'block';
        // 设置必填字段
        document.getElementById('user_id').required = false;
        document.getElementById('cookies').required = true;
    } else {
        monitorConfig.style.display = 'none';
        uploadConfig.style.display = 'none';
        document.getElementById('user_id').required = false;
        document.getElementById('cookies').required = false;
    }
});

document.getElementById('accountForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = {};
    
    // 收集表单数据
    for (let [key, value] of formData.entries()) {
        if (key === 'is_active') {
            data[key] = true;
        } else {
            data[key] = value;
        }
    }
    
    // 处理未选中的复选框
    if (!formData.has('is_active')) {
        data.is_active = false;
    }
    
    // 构建配置对象
    if (data.account_type === 'monitor') {
        data.config = {
            user_id: data.user_id,
            proxy: data.proxy || null
        };
        // 删除已包含在配置中的字段
        delete data.user_id;
        delete data.proxy;
        delete data.cookies;
        delete data.upload_interval;
        delete data.max_daily_uploads;
    } else if (data.account_type === 'upload') {
        data.config = {
            cookies: data.cookies,
            upload_interval: parseInt(data.upload_interval) || 3600,
            max_daily_uploads: parseInt(data.max_daily_uploads) || 10
        };
        // 删除已包含在配置中的字段
        delete data.cookies;
        delete data.upload_interval;
        delete data.max_daily_uploads;
        delete data.user_id;
        delete data.proxy;
    }
    
    // 提交表单
    fetch(this.action, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = "{{ url_for('accounts.list_accounts') }}";
        } else {
            alert('创建失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('创建失败，请重试');
    });
});
</script>
{% endblock %}
