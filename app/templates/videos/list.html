{% extends "base.html" %}

{% block title %}视频管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">视频管理</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button class="btn btn-sm btn-outline-secondary" onclick="refreshList()">
                            <i class="fas fa-sync me-1"></i>刷新
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 过滤器 -->
            <div class="card mb-3">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="platform" class="form-label">原始平台</label>
                            <select class="form-select" id="platform" name="platform">
                                <option value="">全部平台</option>
                                <option value="tiktok" {% if platform_filter == 'tiktok' %}selected{% endif %}>TikTok</option>
                                <option value="douyin" {% if platform_filter == 'douyin' %}selected{% endif %}>抖音</option>
                                <option value="bilibili" {% if platform_filter == 'bilibili' %}selected{% endif %}>哔哩哔哩</option>
                                <option value="xhs" {% if platform_filter == 'xhs' %}selected{% endif %}>小红书</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">状态</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">全部状态</option>
                                <option value="downloaded" {% if status_filter == 'downloaded' %}selected{% endif %}>已下载</option>
                                <option value="processed" {% if status_filter == 'processed' %}selected{% endif %}>已处理</option>
                                <option value="uploaded" {% if status_filter == 'uploaded' %}selected{% endif %}>已上传</option>
                                <option value="failed" {% if status_filter == 'failed' %}selected{% endif %}>失败</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="task_id" class="form-label">任务ID</label>
                            <input type="text" class="form-control" id="task_id" name="task_id" 
                                   value="{{ task_filter }}" placeholder="输入任务ID">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>筛选
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- 视频列表 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">视频列表 (共 {{ pagination.total }} 个)</h5>
                </div>
                <div class="card-body p-0">
                    {% if videos %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>标题</th>
                                    <th>原始平台</th>
                                    <th>任务</th>
                                    <th>下载状态</th>
                                    <th>处理状态</th>
                                    <th>上传状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for video in videos %}
                                <tr>
                                    <td>{{ video.id }}</td>
                                    <td>
                                        <strong>{{ video.title[:30] }}{% if video.title|length > 30 %}...{% endif %}</strong>
                                        {% if video.description %}
                                        <br><small class="text-muted">{{ video.description[:50] }}{% if video.description|length > 50 %}...{% endif %}</small>
                                        {% endif %}
                                        {% if video.original_url %}
                                        <br><small><a href="{{ video.original_url }}" target="_blank" class="text-info">原始链接</a></small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if video.original_platform == 'tiktok' %}
                                        <span class="badge bg-dark">TikTok</span>
                                        {% elif video.original_platform == 'douyin' %}
                                        <span class="badge bg-primary">抖音</span>
                                        {% elif video.original_platform == 'bilibili' %}
                                        <span class="badge bg-info">哔哩哔哩</span>
                                        {% elif video.original_platform == 'xhs' %}
                                        <span class="badge bg-danger">小红书</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ video.original_platform }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if video.task %}
                                        <a href="{{ url_for('tasks.view_task', task_id=video.task_id) }}" class="text-decoration-none">
                                            {{ video.task.name }}
                                        </a>
                                        {% else %}
                                        <span class="text-muted">无关联任务</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if video.download_status == 'completed' %}
                                        <span class="badge bg-success">已完成</span>
                                        {% elif video.download_status == 'failed' %}
                                        <span class="badge bg-danger">失败</span>
                                        {% elif video.download_status == 'downloading' %}
                                        <span class="badge bg-warning">下载中</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ video.download_status or '未开始' }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if video.process_status == 'completed' %}
                                        <span class="badge bg-success">已完成</span>
                                        {% elif video.process_status == 'failed' %}
                                        <span class="badge bg-danger">失败</span>
                                        {% elif video.process_status == 'processing' %}
                                        <span class="badge bg-warning">处理中</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ video.process_status or '未开始' }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if video.upload_status == 'completed' %}
                                        <span class="badge bg-success">已完成</span>
                                        {% elif video.upload_status == 'failed' %}
                                        <span class="badge bg-danger">失败</span>
                                        {% elif video.upload_status == 'uploading' %}
                                        <span class="badge bg-warning">上传中</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ video.upload_status or '未开始' }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {{ video.created_at.strftime('%Y-%m-%d %H:%M') }}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{{ url_for('videos.view_video', video_id=video.id) }}" 
                                               class="btn btn-outline-info" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if video.local_path and video.download_status == 'completed' %}
                                            <a href="{{ url_for('videos.download_video', video_id=video.id) }}" 
                                               class="btn btn-outline-success" title="下载文件">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            {% endif %}
                                            {% if video.process_status == 'failed' or video.upload_status == 'failed' %}
                                            <button class="btn btn-outline-warning" 
                                                    onclick="retryVideo({{ video.id }})" title="重试">
                                                <i class="fas fa-redo"></i>
                                            </button>
                                            {% endif %}
                                            <button class="btn btn-outline-danger" 
                                                    onclick="deleteVideo({{ video.id }})" title="删除">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-video fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无视频数据</p>
                        <p class="text-muted">视频将通过任务自动下载和处理</p>
                    </div>
                    {% endif %}
                </div>
                
                {% if pagination.pages > 1 %}
                <div class="card-footer">
                    <nav aria-label="视频分页">
                        <ul class="pagination justify-content-center mb-0">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('videos.list_videos', page=pagination.prev_num, platform=platform_filter, status=status_filter, task_id=task_filter, per_page=per_page) }}">上一页</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in pagination.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('videos.list_videos', page=page_num, platform=platform_filter, status=status_filter, task_id=task_filter, per_page=per_page) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('videos.list_videos', page=pagination.next_num, platform=platform_filter, status=status_filter, task_id=task_filter, per_page=per_page) }}">下一页</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
function refreshList() {
    location.reload();
}

function retryVideo(videoId) {
    if (confirm('确定要重试处理这个视频吗？')) {
        fetch(`/videos/${videoId}/retry`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('重试请求已提交');
                location.reload();
            } else {
                alert('重试失败: ' + data.message);
            }
        })
        .catch(error => {
            alert('重试失败: ' + error);
        });
    }
}

function deleteVideo(videoId) {
    if (confirm('确定要删除这个视频吗？此操作不可恢复！')) {
        fetch(`/videos/${videoId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('删除失败: ' + data.message);
            }
        })
        .catch(error => {
            alert('删除失败: ' + error);
        });
    }
}
</script>
{% endblock %}
