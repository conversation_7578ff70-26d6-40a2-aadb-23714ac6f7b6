{% extends "base.html" %}

{% block title %}仪表板 - 视频监控管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>
            系统仪表板
        </h1>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card card-stats primary h-100">
            <div class="card-body">
                <div class="row">
                    <div class="col">
                        <h5 class="card-title text-uppercase text-muted mb-0">活跃任务</h5>
                        <span class="h2 font-weight-bold mb-0" id="active-tasks">{{ stats.active_tasks }}</span>
                    </div>
                    <div class="col-auto">
                        <div class="icon icon-shape bg-primary text-white rounded-circle shadow">
                            <i class="fas fa-tasks"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card card-stats success h-100">
            <div class="card-body">
                <div class="row">
                    <div class="col">
                        <h5 class="card-title text-uppercase text-muted mb-0">今日下载</h5>
                        <span class="h2 font-weight-bold mb-0" id="today-downloads">{{ stats.today_downloads }}</span>
                    </div>
                    <div class="col-auto">
                        <div class="icon icon-shape bg-success text-white rounded-circle shadow">
                            <i class="fas fa-download"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card card-stats warning h-100">
            <div class="card-body">
                <div class="row">
                    <div class="col">
                        <h5 class="card-title text-uppercase text-muted mb-0">今日上传</h5>
                        <span class="h2 font-weight-bold mb-0" id="today-uploads">{{ stats.today_uploads }}</span>
                    </div>
                    <div class="col-auto">
                        <div class="icon icon-shape bg-warning text-white rounded-circle shadow">
                            <i class="fas fa-upload"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card card-stats danger h-100">
            <div class="card-body">
                <div class="row">
                    <div class="col">
                        <h5 class="card-title text-uppercase text-muted mb-0">错误任务</h5>
                        <span class="h2 font-weight-bold mb-0" id="error-tasks">{{ stats.error_tasks }}</span>
                    </div>
                    <div class="col-auto">
                        <div class="icon icon-shape bg-danger text-white rounded-circle shadow">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图表和活动 -->
<div class="row">
    <!-- 活动图表 -->
    <div class="col-xl-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    系统活动趋势
                </h5>
            </div>
            <div class="card-body">
                <canvas id="activityChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <!-- 平台分布 -->
    <div class="col-xl-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    平台分布
                </h5>
            </div>
            <div class="card-body">
                <canvas id="platformChart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 最近活动和系统状态 -->
<div class="row">
    <!-- 最近活动 -->
    <div class="col-xl-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>
                    最近活动
                </h5>
                <button class="btn btn-sm btn-outline-primary" onclick="refreshRecentActivity()">
                    <i class="fas fa-refresh"></i>
                </button>
            </div>
            <div class="card-body">
                <div id="recent-activities">
                    {% for activity in recent_activities %}
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="icon-circle bg-{{ activity.type }} text-white">
                                <i class="fas fa-{{ activity.icon }}"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="small text-muted">{{ activity.created_at | datetime }}</div>
                            <div>{{ activity.message }}</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- 系统状态 -->
    <div class="col-xl-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-server me-2"></i>
                    系统状态
                </h5>
                <button class="btn btn-sm btn-outline-primary" onclick="refreshSystemStatus()">
                    <i class="fas fa-refresh"></i>
                </button>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 mb-0 text-primary" id="monitor-queue">{{ system_status.monitor_queue }}</div>
                            <div class="small text-muted">监控队列</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 mb-0 text-success" id="process-queue">{{ system_status.process_queue }}</div>
                            <div class="small text-muted">处理队列</div>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 mb-0 text-warning" id="upload-queue">{{ system_status.upload_queue }}</div>
                            <div class="small text-muted">上传队列</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 mb-0 text-info" id="active-accounts">{{ system_status.active_accounts }}</div>
                            <div class="small text-muted">活跃账号</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    快速操作
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('tasks.create_task') }}" class="btn btn-primary w-100">
                            <i class="fas fa-plus me-2"></i>
                            创建任务
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('accounts.create_account') }}" class="btn btn-success w-100">
                            <i class="fas fa-user-plus me-2"></i>
                            添加账号
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-warning w-100" onclick="retryFailedTasks()">
                            <i class="fas fa-redo me-2"></i>
                            重试失败任务
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-info w-100" onclick="cleanupSystem()">
                            <i class="fas fa-broom me-2"></i>
                            系统清理
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 活动趋势图表
const activityCtx = document.getElementById('activityChart').getContext('2d');
const activityChart = new Chart(activityCtx, {
    type: 'line',
    data: {
        labels: {{ chart_data.labels | tojson }},
        datasets: [{
            label: '下载',
            data: {{ chart_data.downloads | tojson }},
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }, {
            label: '上传',
            data: {{ chart_data.uploads | tojson }},
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// 平台分布图表
const platformCtx = document.getElementById('platformChart').getContext('2d');
const platformChart = new Chart(platformCtx, {
    type: 'doughnut',
    data: {
        labels: {{ platform_data.labels | tojson }},
        datasets: [{
            data: {{ platform_data.data | tojson }},
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: true
    }
});

// 刷新最近活动
function refreshRecentActivity() {
    fetch('/api/recent-activities')
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('recent-activities');
            container.innerHTML = '';
            
            data.activities.forEach(activity => {
                const activityDiv = document.createElement('div');
                activityDiv.className = 'd-flex align-items-center mb-3';
                activityDiv.innerHTML = `
                    <div class="flex-shrink-0">
                        <div class="icon-circle bg-${activity.type} text-white">
                            <i class="fas fa-${activity.icon}"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="small text-muted">${formatDateTime(activity.created_at)}</div>
                        <div>${activity.message}</div>
                    </div>
                `;
                container.appendChild(activityDiv);
            });
        })
        .catch(error => {
            console.error('刷新活动失败:', error);
            showAlert('刷新活动失败', 'danger');
        });
}

// 刷新系统状态
function refreshSystemStatus() {
    fetch('/api/system-status')
        .then(response => response.json())
        .then(data => {
            document.getElementById('monitor-queue').textContent = data.monitor_queue;
            document.getElementById('process-queue').textContent = data.process_queue;
            document.getElementById('upload-queue').textContent = data.upload_queue;
            document.getElementById('active-accounts').textContent = data.active_accounts;
        })
        .catch(error => {
            console.error('刷新系统状态失败:', error);
            showAlert('刷新系统状态失败', 'danger');
        });
}

// 重试失败任务
function retryFailedTasks() {
    if (!confirm('确定要重试所有失败的任务吗？')) {
        return;
    }
    
    fetch('/api/retry-failed-tasks', { method: 'POST' })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(`成功重试了 ${data.retried_count} 个任务`, 'success');
                setTimeout(() => location.reload(), 2000);
            } else {
                showAlert(data.error, 'danger');
            }
        })
        .catch(error => {
            console.error('重试任务失败:', error);
            showAlert('重试任务失败', 'danger');
        });
}

// 系统清理
function cleanupSystem() {
    if (!confirm('确定要执行系统清理吗？这将清理过期的日志和临时文件。')) {
        return;
    }
    
    fetch('/api/cleanup-system', { method: 'POST' })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('系统清理完成', 'success');
            } else {
                showAlert(data.error, 'danger');
            }
        })
        .catch(error => {
            console.error('系统清理失败:', error);
            showAlert('系统清理失败', 'danger');
        });
}

// 自动刷新数据
setInterval(() => {
    refreshSystemStatus();
}, 30000); // 每30秒刷新一次

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 添加图标圆圈样式
    const style = document.createElement('style');
    style.textContent = `
        .icon-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .icon-shape {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }
    `;
    document.head.appendChild(style);
});
</script>
{% endblock %}
