from app import db
from datetime import datetime
import json

class Account(db.Model):
    """账号模型 - 存储各平台账号信息"""
    __tablename__ = 'accounts'
    
    id = db.Column(db.Integer, primary_key=True)
    platform = db.Column(db.String(50), nullable=False)  # 平台名称
    account_type = db.Column(db.String(20), nullable=False)  # 'monitor' 或 'upload'
    username = db.Column(db.String(100), nullable=False)  # 用户名
    display_name = db.Column(db.String(100))  # 显示名称
    user_id = db.Column(db.String(100))  # 平台用户ID（监控账号必填）
    
    # 认证信息（JSON格式存储）
    cookies = db.Column(db.Text)  # cookies信息
    auth_data = db.Column(db.Text)  # 其他认证数据
    
    # 状态信息
    is_active = db.Column(db.<PERSON>, default=True)  # 是否启用
    last_check = db.Column(db.DateTime)  # 最后检查时间
    status = db.Column(db.String(20), default='unknown')  # 状态：active, expired, error
    error_message = db.Column(db.Text)  # 错误信息
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    monitor_tasks = db.relationship('Task', foreign_keys='Task.monitor_account_id', backref='monitor_account')
    upload_tasks = db.relationship('Task', foreign_keys='Task.upload_account_id', backref='upload_account')
    
    def __repr__(self):
        return f'<Account {self.platform}:{self.username}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'platform': self.platform,
            'account_type': self.account_type,
            'username': self.username,
            'display_name': self.display_name,
            'user_id': self.user_id,
            'is_active': self.is_active,
            'last_check': self.last_check.isoformat() if self.last_check else None,
            'status': self.status,
            'error_message': self.error_message,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def get_cookies_dict(self):
        """获取cookies字典"""
        if self.cookies:
            try:
                return json.loads(self.cookies)
            except json.JSONDecodeError:
                return {}
        return {}
    
    def set_cookies_dict(self, cookies_dict):
        """设置cookies字典"""
        self.cookies = json.dumps(cookies_dict)
    
    def get_auth_data_dict(self):
        """获取认证数据字典"""
        if self.auth_data:
            try:
                return json.loads(self.auth_data)
            except json.JSONDecodeError:
                return {}
        return {}
    
    def set_auth_data_dict(self, auth_dict):
        """设置认证数据字典"""
        self.auth_data = json.dumps(auth_dict)
    
    def update_status(self, status, error_message=None):
        """更新账号状态"""
        self.status = status
        self.error_message = error_message
        self.last_check = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        db.session.commit()
    
    @staticmethod
    def get_by_platform_and_type(platform, account_type):
        """根据平台和类型获取账号列表"""
        return Account.query.filter_by(
            platform=platform, 
            account_type=account_type,
            is_active=True
        ).all()
    
    @staticmethod
    def get_monitor_accounts(platform=None):
        """获取监控账号"""
        query = Account.query.filter_by(account_type='monitor', is_active=True)
        if platform:
            query = query.filter_by(platform=platform)
        return query.all()
    
    @staticmethod
    def get_upload_accounts(platform=None):
        """获取上传账号"""
        query = Account.query.filter_by(account_type='upload', is_active=True)
        if platform:
            query = query.filter_by(platform=platform)
        return query.all()
