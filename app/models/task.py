from app import db
from datetime import datetime
import json

class Task(db.Model):
    """任务模型"""
    __tablename__ = 'tasks'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)  # 任务名称
    description = db.Column(db.Text)  # 任务描述
    
    # 监控配置
    monitor_platform = db.Column(db.String(50), nullable=False)  # 监控平台
    monitor_account_id = db.Column(db.Integer, db.ForeignKey('accounts.id'), nullable=False)
    monitor_interval = db.Column(db.Integer, default=300)  # 监控间隔（秒）
    
    # 处理配置
    enable_deduplication = db.Column(db.<PERSON>, default=True)  # 启用去重
    enable_pseudo_original = db.Column(db.Bo<PERSON>an, default=False)  # 启用伪原创
    processing_config = db.Column(db.Text)  # 处理配置（JSON格式）
    
    # 上传配置
    upload_platform = db.Column(db.String(50), nullable=False)  # 上传平台
    upload_account_id = db.Column(db.Integer, db.<PERSON>ey('accounts.id'), nullable=False)
    upload_config = db.Column(db.Text)  # 上传配置（JSON格式）
    
    # 任务状态
    status = db.Column(db.String(20), default='stopped')  # stopped, running, paused, error
    is_active = db.Column(db.Boolean, default=True)  # 是否启用
    
    # 统计信息
    total_monitored = db.Column(db.Integer, default=0)  # 总监控次数
    total_downloaded = db.Column(db.Integer, default=0)  # 总下载数
    total_uploaded = db.Column(db.Integer, default=0)  # 总上传数
    last_run = db.Column(db.DateTime)  # 最后运行时间
    next_run = db.Column(db.DateTime)  # 下次运行时间
    
    # 错误信息
    error_message = db.Column(db.Text)  # 错误信息
    error_count = db.Column(db.Integer, default=0)  # 错误次数
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    logs = db.relationship('TaskLog', backref='task', lazy='dynamic', cascade='all, delete-orphan')
    videos = db.relationship('Video', backref='task', lazy='dynamic')
    
    def __repr__(self):
        return f'<Task {self.name}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'monitor_platform': self.monitor_platform,
            'monitor_account_id': self.monitor_account_id,
            'monitor_interval': self.monitor_interval,
            'enable_deduplication': self.enable_deduplication,
            'enable_pseudo_original': self.enable_pseudo_original,
            'upload_platform': self.upload_platform,
            'upload_account_id': self.upload_account_id,
            'status': self.status,
            'is_active': self.is_active,
            'total_monitored': self.total_monitored,
            'total_downloaded': self.total_downloaded,
            'total_uploaded': self.total_uploaded,
            'last_run': self.last_run.isoformat() if self.last_run else None,
            'next_run': self.next_run.isoformat() if self.next_run else None,
            'error_message': self.error_message,
            'error_count': self.error_count,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def get_processing_config(self):
        """获取处理配置"""
        if self.processing_config:
            try:
                return json.loads(self.processing_config)
            except json.JSONDecodeError:
                return {}
        return {}
    
    def set_processing_config(self, config_dict):
        """设置处理配置"""
        self.processing_config = json.dumps(config_dict)
    
    def get_upload_config(self):
        """获取上传配置"""
        if self.upload_config:
            try:
                return json.loads(self.upload_config)
            except json.JSONDecodeError:
                return {}
        return {}
    
    def set_upload_config(self, config_dict):
        """设置上传配置"""
        self.upload_config = json.dumps(config_dict)
    
    def update_status(self, status, error_message=None):
        """更新任务状态"""
        self.status = status
        if error_message:
            self.error_message = error_message
            self.error_count += 1
        else:
            self.error_message = None
        self.updated_at = datetime.utcnow()
        db.session.commit()
    
    def add_log(self, level, message, details=None):
        """添加任务日志"""
        log = TaskLog(
            task_id=self.id,
            level=level,
            message=message,
            details=details
        )
        db.session.add(log)
        db.session.commit()
        return log

class TaskLog(db.Model):
    """任务日志模型"""
    __tablename__ = 'task_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    task_id = db.Column(db.Integer, db.ForeignKey('tasks.id'), nullable=False)
    level = db.Column(db.String(20), nullable=False)  # INFO, WARNING, ERROR
    message = db.Column(db.Text, nullable=False)  # 日志消息
    details = db.Column(db.Text)  # 详细信息（JSON格式）
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<TaskLog {self.level}: {self.message[:50]}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'task_id': self.task_id,
            'level': self.level,
            'message': self.message,
            'details': self.details,
            'created_at': self.created_at.isoformat()
        }
