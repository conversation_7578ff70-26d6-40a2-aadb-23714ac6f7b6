from app import db
from datetime import datetime
import json
import os

class Video(db.Model):
    """视频模型"""
    __tablename__ = 'videos'
    
    id = db.Column(db.Integer, primary_key=True)
    task_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>ey('tasks.id'), nullable=False)
    
    # 原始视频信息
    original_platform = db.Column(db.String(50), nullable=False)  # 原始平台
    original_video_id = db.Column(db.String(200), nullable=False)  # 原始视频ID
    original_url = db.Column(db.String(500))  # 原始URL
    original_title = db.Column(db.Text)  # 原始标题
    original_description = db.Column(db.Text)  # 原始描述
    original_author = db.Column(db.String(200))  # 原始作者
    original_author_id = db.Column(db.String(200))  # 原始作者ID
    
    # 文件信息
    local_path = db.Column(db.String(500))  # 本地文件路径
    file_name = db.Column(db.String(200))  # 文件名
    file_size = db.Column(db.BigInteger)  # 文件大小（字节）
    duration = db.Column(db.Integer)  # 视频时长（秒）
    resolution = db.Column(db.String(20))  # 分辨率
    format = db.Column(db.String(10))  # 视频格式
    
    # 处理状态
    download_status = db.Column(db.String(20), default='pending')  # pending, downloading, completed, failed
    process_status = db.Column(db.String(20), default='pending')  # pending, processing, completed, failed, skipped
    upload_status = db.Column(db.String(20), default='pending')  # pending, uploading, completed, failed, skipped
    
    # 处理后信息
    processed_path = db.Column(db.String(500))  # 处理后文件路径
    processed_title = db.Column(db.Text)  # 处理后标题
    processed_description = db.Column(db.Text)  # 处理后描述
    processing_config = db.Column(db.Text)  # 处理配置（JSON格式）
    
    # 上传信息
    upload_platform = db.Column(db.String(50))  # 上传平台
    upload_video_id = db.Column(db.String(200))  # 上传后的视频ID
    upload_url = db.Column(db.String(500))  # 上传后的URL
    upload_config = db.Column(db.Text)  # 上传配置（JSON格式）
    
    # 去重信息
    content_hash = db.Column(db.String(64))  # 内容哈希
    is_duplicate = db.Column(db.Boolean, default=False)  # 是否重复
    duplicate_of = db.Column(db.Integer, db.ForeignKey('videos.id'))  # 重复的原视频ID
    
    # 时间戳
    original_publish_time = db.Column(db.DateTime)  # 原始发布时间
    downloaded_at = db.Column(db.DateTime)  # 下载时间
    processed_at = db.Column(db.DateTime)  # 处理时间
    uploaded_at = db.Column(db.DateTime)  # 上传时间
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 错误信息
    error_message = db.Column(db.Text)  # 错误信息
    retry_count = db.Column(db.Integer, default=0)  # 重试次数
    
    # 关系
    duplicates = db.relationship('Video', backref=db.backref('original_video', remote_side=[id]))
    process_logs = db.relationship('VideoProcessLog', backref='video', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Video {self.original_video_id}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'task_id': self.task_id,
            'original_platform': self.original_platform,
            'original_video_id': self.original_video_id,
            'original_url': self.original_url,
            'original_title': self.original_title,
            'original_description': self.original_description,
            'original_author': self.original_author,
            'original_author_id': self.original_author_id,
            'local_path': self.local_path,
            'file_name': self.file_name,
            'file_size': self.file_size,
            'duration': self.duration,
            'resolution': self.resolution,
            'format': self.format,
            'download_status': self.download_status,
            'process_status': self.process_status,
            'upload_status': self.upload_status,
            'processed_path': self.processed_path,
            'processed_title': self.processed_title,
            'processed_description': self.processed_description,
            'upload_platform': self.upload_platform,
            'upload_video_id': self.upload_video_id,
            'upload_url': self.upload_url,
            'content_hash': self.content_hash,
            'is_duplicate': self.is_duplicate,
            'duplicate_of': self.duplicate_of,
            'original_publish_time': self.original_publish_time.isoformat() if self.original_publish_time else None,
            'downloaded_at': self.downloaded_at.isoformat() if self.downloaded_at else None,
            'processed_at': self.processed_at.isoformat() if self.processed_at else None,
            'uploaded_at': self.uploaded_at.isoformat() if self.uploaded_at else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'error_message': self.error_message,
            'retry_count': self.retry_count
        }
    
    def get_processing_config(self):
        """获取处理配置"""
        if self.processing_config:
            try:
                return json.loads(self.processing_config)
            except json.JSONDecodeError:
                return {}
        return {}
    
    def set_processing_config(self, config_dict):
        """设置处理配置"""
        self.processing_config = json.dumps(config_dict)
    
    def get_upload_config(self):
        """获取上传配置"""
        if self.upload_config:
            try:
                return json.loads(self.upload_config)
            except json.JSONDecodeError:
                return {}
        return {}
    
    def set_upload_config(self, config_dict):
        """设置上传配置"""
        self.upload_config = json.dumps(config_dict)
    
    def update_status(self, status_type, status, error_message=None):
        """更新状态"""
        if status_type == 'download':
            self.download_status = status
            if status == 'completed':
                self.downloaded_at = datetime.utcnow()
        elif status_type == 'process':
            self.process_status = status
            if status == 'completed':
                self.processed_at = datetime.utcnow()
        elif status_type == 'upload':
            self.upload_status = status
            if status == 'completed':
                self.uploaded_at = datetime.utcnow()
        
        if error_message:
            self.error_message = error_message
            self.retry_count += 1
        
        self.updated_at = datetime.utcnow()
        db.session.commit()
    
    def add_process_log(self, operation, status, message, details=None):
        """添加处理日志"""
        log = VideoProcessLog(
            video_id=self.id,
            operation=operation,
            status=status,
            message=message,
            details=details
        )
        db.session.add(log)
        db.session.commit()
        return log
    
    def get_file_exists(self):
        """检查文件是否存在"""
        if self.local_path and os.path.exists(self.local_path):
            return True
        if self.processed_path and os.path.exists(self.processed_path):
            return True
        return False

class VideoProcessLog(db.Model):
    """视频处理日志模型"""
    __tablename__ = 'video_process_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    video_id = db.Column(db.Integer, db.ForeignKey('videos.id'), nullable=False)
    operation = db.Column(db.String(50), nullable=False)  # download, process, upload
    status = db.Column(db.String(20), nullable=False)  # started, completed, failed
    message = db.Column(db.Text, nullable=False)  # 日志消息
    details = db.Column(db.Text)  # 详细信息（JSON格式）
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<VideoProcessLog {self.operation}: {self.status}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'video_id': self.video_id,
            'operation': self.operation,
            'status': self.status,
            'message': self.message,
            'details': self.details,
            'created_at': self.created_at.isoformat()
        }
