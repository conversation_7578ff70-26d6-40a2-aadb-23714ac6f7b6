from flask import Blueprint, render_template, jsonify
from app.models import Task, Account, Video
from app import db
from sqlalchemy import func

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """主页 - 显示系统概览"""
    # 获取统计数据
    stats = {
        'total_tasks': Task.query.count(),
        'active_tasks': Task.query.filter_by(is_active=True).count(),
        'running_tasks': Task.query.filter_by(status='running').count(),
        'total_accounts': Account.query.count(),
        'monitor_accounts': Account.query.filter_by(account_type='monitor', is_active=True).count(),
        'upload_accounts': Account.query.filter_by(account_type='upload', is_active=True).count(),
        'total_videos': Video.query.count(),
        'downloaded_videos': Video.query.filter_by(download_status='completed').count(),
        'uploaded_videos': Video.query.filter_by(upload_status='completed').count(),
    }
    
    # 获取最近的任务
    recent_tasks = Task.query.order_by(Task.updated_at.desc()).limit(5).all()
    
    # 获取最近的视频
    recent_videos = Video.query.order_by(Video.created_at.desc()).limit(10).all()
    
    return render_template('index.html', 
                         stats=stats, 
                         recent_tasks=recent_tasks, 
                         recent_videos=recent_videos)

@main_bp.route('/api/stats')
def api_stats():
    """API - 获取系统统计数据"""
    stats = {
        'tasks': {
            'total': Task.query.count(),
            'active': Task.query.filter_by(is_active=True).count(),
            'running': Task.query.filter_by(status='running').count(),
            'stopped': Task.query.filter_by(status='stopped').count(),
            'error': Task.query.filter_by(status='error').count(),
        },
        'accounts': {
            'total': Account.query.count(),
            'monitor': Account.query.filter_by(account_type='monitor', is_active=True).count(),
            'upload': Account.query.filter_by(account_type='upload', is_active=True).count(),
            'active': Account.query.filter_by(is_active=True).count(),
        },
        'videos': {
            'total': Video.query.count(),
            'downloaded': Video.query.filter_by(download_status='completed').count(),
            'processed': Video.query.filter_by(process_status='completed').count(),
            'uploaded': Video.query.filter_by(upload_status='completed').count(),
            'failed': Video.query.filter(
                (Video.download_status == 'failed') |
                (Video.process_status == 'failed') |
                (Video.upload_status == 'failed')
            ).count(),
        }
    }
    
    # 按平台统计
    platform_stats = {}
    
    # 监控平台统计
    monitor_platforms = db.session.query(
        Task.monitor_platform, 
        func.count(Task.id).label('count')
    ).group_by(Task.monitor_platform).all()
    
    for platform, count in monitor_platforms:
        if platform not in platform_stats:
            platform_stats[platform] = {}
        platform_stats[platform]['monitor_tasks'] = count
    
    # 上传平台统计
    upload_platforms = db.session.query(
        Task.upload_platform, 
        func.count(Task.id).label('count')
    ).group_by(Task.upload_platform).all()
    
    for platform, count in upload_platforms:
        if platform not in platform_stats:
            platform_stats[platform] = {}
        platform_stats[platform]['upload_tasks'] = count
    
    # 视频平台统计
    video_platforms = db.session.query(
        Video.original_platform, 
        func.count(Video.id).label('count')
    ).group_by(Video.original_platform).all()
    
    for platform, count in video_platforms:
        if platform not in platform_stats:
            platform_stats[platform] = {}
        platform_stats[platform]['videos'] = count
    
    stats['platforms'] = platform_stats
    
    return jsonify(stats)

@main_bp.route('/api/recent-activities')
def api_recent_activities():
    """API - 获取最近活动"""
    activities = []
    
    # 最近的任务日志
    from app.models import TaskLog
    recent_task_logs = TaskLog.query.order_by(TaskLog.created_at.desc()).limit(10).all()
    
    for log in recent_task_logs:
        activities.append({
            'type': 'task_log',
            'level': log.level,
            'message': f"任务 '{log.task.name}': {log.message}",
            'time': log.created_at.isoformat(),
            'task_id': log.task_id
        })
    
    # 最近的视频处理日志
    from app.models import VideoProcessLog
    recent_video_logs = VideoProcessLog.query.order_by(VideoProcessLog.created_at.desc()).limit(10).all()
    
    for log in recent_video_logs:
        activities.append({
            'type': 'video_log',
            'operation': log.operation,
            'status': log.status,
            'message': f"视频处理: {log.message}",
            'time': log.created_at.isoformat(),
            'video_id': log.video_id
        })
    
    # 按时间排序
    activities.sort(key=lambda x: x['time'], reverse=True)
    
    return jsonify(activities[:20])  # 返回最近20条活动

@main_bp.route('/health')
def health_check():
    """健康检查接口"""
    try:
        # 检查数据库连接
        db.session.execute('SELECT 1')
        
        return jsonify({
            'status': 'healthy',
            'database': 'connected',
            'timestamp': db.func.now()
        })
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': db.func.now()
        }), 500
