from flask import Blueprint, request, jsonify
from app.models import Account, Task, Video
from app import db
from datetime import datetime
import json

api_bp = Blueprint('api', __name__, url_prefix='/api')

@api_bp.route('/status')
def get_status():
    """获取系统状态"""
    try:
        total_accounts = Account.query.count()
        active_accounts = Account.query.filter_by(is_active=True).count()
        total_tasks = Task.query.count()
        active_tasks = Task.query.filter_by(is_active=True).count()
        total_videos = Video.query.count()
        
        return jsonify({
            'status': 'ok',
            'timestamp': datetime.now().isoformat(),
            'accounts': {
                'total': total_accounts,
                'active': active_accounts
            },
            'tasks': {
                'total': total_tasks,
                'active': active_tasks
            },
            'videos': {
                'total': total_videos
            },
            'system': {
                'task_scheduler_running': True,
                'processor_running': True,
                'uploader_running': True,
                'monitor_running': True
            }
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api_bp.route('/accounts', methods=['GET', 'POST'])
def accounts_api():
    """账号API"""
    if request.method == 'GET':
        try:
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 20, type=int)
            platform = request.args.get('platform')
            account_type = request.args.get('account_type')
            
            query = Account.query
            if platform:
                query = query.filter_by(platform=platform)
            if account_type:
                query = query.filter_by(account_type=account_type)
            
            accounts = query.paginate(
                page=page, per_page=per_page, error_out=False
            )
            
            return jsonify({
                'accounts': [account.to_dict() for account in accounts.items],
                'total': accounts.total,
                'pages': accounts.pages,
                'current_page': page
            })
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    
    elif request.method == 'POST':
        try:
            data = request.get_json()
            
            # 验证必填字段
            required_fields = ['name', 'platform', 'account_type']
            for field in required_fields:
                if not data.get(field):
                    return jsonify({'error': f'缺少必填字段: {field}'}), 400
            
            # 验证账号类型
            if data['account_type'] not in ['monitor', 'upload']:
                return jsonify({'error': '账号类型必须是 monitor 或 upload'}), 400
            
            # 监控账号必须有配置中的 user_id
            if data['account_type'] == 'monitor':
                config = data.get('config', {})
                if not config.get('user_id'):
                    return jsonify({'error': '监控账号必须填写目标用户ID'}), 400
            
            # 上传账号必须有配置中的 cookies
            if data['account_type'] == 'upload':
                config = data.get('config', {})
                if not config.get('cookies'):
                    return jsonify({'error': '上传账号必须填写cookies'}), 400
            
            # 检查账号是否已存在
            existing = Account.query.filter_by(
                platform=data['platform'],
                account_type=data['account_type'],
                name=data['name']
            ).first()
            
            if existing:
                return jsonify({'error': '该账号名称已存在'}), 400
            
            # 创建账号
            account = Account(
                name=data['name'],
                platform=data['platform'],
                account_type=data['account_type'],
                username=data.get('username', ''),
                description=data.get('description', ''),
                is_active=bool(data.get('is_active', True))
            )
            
            # 设置配置信息
            config = data.get('config', {})
            if config:
                if data['account_type'] == 'monitor':
                    # 监控账号配置
                    account.user_id = config.get('user_id', '')
                    # 可以存储其他监控配置到 auth_data
                    if config.get('proxy'):
                        account.set_auth_data_dict({'proxy': config['proxy']})
                elif data['account_type'] == 'upload':
                    # 上传账号配置
                    account.cookies = config.get('cookies', '')
                    # 存储上传配置到 auth_data
                    upload_config = {
                        'upload_interval': config.get('upload_interval', 3600),
                        'max_daily_uploads': config.get('max_daily_uploads', 10)
                    }
                    account.set_auth_data_dict(upload_config)
            
            db.session.add(account)
            db.session.commit()
            
            return jsonify(account.to_dict()), 201
            
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

@api_bp.route('/accounts/<int:account_id>', methods=['GET', 'PUT', 'DELETE'])
def account_detail_api(account_id):
    """账号详情API"""
    account = Account.query.get_or_404(account_id)
    
    if request.method == 'GET':
        return jsonify(account.to_dict())
    
    elif request.method == 'PUT':
        try:
            data = request.get_json()
            
            # 更新基本信息
            if 'name' in data:
                account.name = data['name']
            if 'username' in data:
                account.username = data['username']
            if 'description' in data:
                account.description = data['description']
            if 'is_active' in data:
                account.is_active = bool(data['is_active'])
            
            # 更新配置信息
            config = data.get('config', {})
            if config:
                if account.account_type == 'monitor':
                    # 监控账号配置
                    if 'user_id' in config:
                        account.user_id = config['user_id']
                    # 更新代理配置
                    auth_data = account.get_auth_data_dict()
                    if 'proxy' in config:
                        auth_data['proxy'] = config['proxy']
                        account.set_auth_data_dict(auth_data)
                elif account.account_type == 'upload':
                    # 上传账号配置
                    if 'cookies' in config:
                        account.cookies = config['cookies']
                    # 更新上传配置
                    auth_data = account.get_auth_data_dict()
                    if 'upload_interval' in config:
                        auth_data['upload_interval'] = config['upload_interval']
                    if 'max_daily_uploads' in config:
                        auth_data['max_daily_uploads'] = config['max_daily_uploads']
                    account.set_auth_data_dict(auth_data)
            
            db.session.commit()
            return jsonify(account.to_dict())
            
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500
    
    elif request.method == 'DELETE':
        try:
            db.session.delete(account)
            db.session.commit()
            return jsonify({'message': '账号删除成功'})
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

@api_bp.route('/tasks', methods=['GET', 'POST'])
def tasks_api():
    """任务API"""
    if request.method == 'GET':
        try:
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 20, type=int)
            platform = request.args.get('platform')
            status = request.args.get('status')
            
            query = Task.query
            if platform:
                query = query.filter_by(monitor_platform=platform)
            if status:
                query = query.filter_by(status=status)
            
            tasks = query.paginate(
                page=page, per_page=per_page, error_out=False
            )
            
            return jsonify({
                'tasks': [task.to_dict() for task in tasks.items],
                'total': tasks.total,
                'pages': tasks.pages,
                'current_page': page
            })
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    
    elif request.method == 'POST':
        try:
            data = request.get_json()
            
            # 验证必填字段
            required_fields = ['name', 'monitor_platform', 'monitor_account_id']
            for field in required_fields:
                if not data.get(field):
                    return jsonify({'error': f'缺少必填字段: {field}'}), 400
            
            # 验证监控账号存在
            monitor_account = Account.query.get(data['monitor_account_id'])
            if not monitor_account:
                return jsonify({'error': '监控账号不存在'}), 400
            
            # 验证上传账号（如果提供）
            upload_account = None
            if data.get('upload_account_id'):
                upload_account = Account.query.get(data['upload_account_id'])
                if not upload_account:
                    return jsonify({'error': '上传账号不存在'}), 400
            
            # 创建任务
            task = Task(
                name=data['name'],
                description=data.get('description', ''),
                monitor_platform=data['monitor_platform'],
                monitor_account_id=data['monitor_account_id'],
                upload_platform=data.get('upload_platform'),
                upload_account_id=data.get('upload_account_id'),
                max_videos=data.get('max_videos', 10),
                auto_upload=bool(data.get('auto_upload', False)),
                is_active=bool(data.get('is_active', True))
            )
            
            # 设置监控配置
            if data.get('monitor_config'):
                task.set_monitor_config(data['monitor_config'])
            
            # 设置处理配置
            if data.get('process_config'):
                task.set_process_config(data['process_config'])
            
            # 设置上传配置
            if data.get('upload_config'):
                task.set_upload_config(data['upload_config'])
            
            db.session.add(task)
            db.session.commit()
            
            return jsonify(task.to_dict()), 201
            
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

@api_bp.route('/videos', methods=['GET'])
def videos_api():
    """视频API"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        platform = request.args.get('platform')
        status = request.args.get('status')
        
        query = Video.query
        if platform:
            query = query.filter_by(platform=platform)
        if status:
            query = query.filter_by(status=status)
        
        videos = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'videos': [video.to_dict() for video in videos.items],
            'total': videos.total,
            'pages': videos.pages,
            'current_page': page
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500
