from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from app.models import Account
from app import db
from datetime import datetime
import json

accounts_bp = Blueprint('accounts', __name__)

@accounts_bp.route('/')
def list_accounts():
    """账号列表页面"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    platform_filter = request.args.get('platform', '')
    type_filter = request.args.get('type', '')
    status_filter = request.args.get('status', '')
    
    query = Account.query
    
    # 应用过滤器
    if platform_filter:
        query = query.filter_by(platform=platform_filter)
    if type_filter:
        query = query.filter_by(account_type=type_filter)
    if status_filter:
        query = query.filter_by(status=status_filter)
    
    accounts = query.order_by(Account.updated_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # 获取所有平台用于过滤器
    platforms = db.session.query(Account.platform).distinct().all()
    platforms = [p[0] for p in platforms]
    
    return render_template('accounts/list.html', 
                         accounts=accounts, 
                         platforms=platforms,
                         current_platform=platform_filter,
                         current_type=type_filter,
                         current_status=status_filter)

@accounts_bp.route('/create', methods=['GET', 'POST'])
def create_account():
    """创建账号"""
    if request.method == 'GET':
        from config import Config
        return render_template('accounts/create.html',
                             monitor_platforms=Config.SUPPORTED_MONITOR_PLATFORMS,
                             upload_platforms=Config.SUPPORTED_UPLOAD_PLATFORMS)
    
    # POST 请求处理
    try:
        data = request.get_json() if request.is_json else request.form
        
        # 验证必填字段
        required_fields = ['platform', 'account_type', 'username']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'缺少必填字段: {field}'}), 400
        
        # 验证账号类型
        if data['account_type'] not in ['monitor', 'upload']:
            return jsonify({'error': '账号类型必须是 monitor 或 upload'}), 400
        
        # 监控账号必须有 user_id
        if data['account_type'] == 'monitor' and not data.get('user_id'):
            return jsonify({'error': '监控账号必须填写用户ID'}), 400
        
        # 检查账号是否已存在
        existing = Account.query.filter_by(
            platform=data['platform'],
            account_type=data['account_type'],
            username=data['username']
        ).first()
        
        if existing:
            return jsonify({'error': '该账号已存在'}), 400
        
        # 创建账号
        account = Account(
            platform=data['platform'],
            account_type=data['account_type'],
            username=data['username'],
            display_name=data.get('display_name', ''),
            user_id=data.get('user_id', '')
        )
        
        # 设置cookies
        if data.get('cookies'):
            if isinstance(data['cookies'], str):
                try:
                    cookies_dict = json.loads(data['cookies'])
                    account.set_cookies_dict(cookies_dict)
                except json.JSONDecodeError:
                    return jsonify({'error': 'cookies格式错误，必须是有效的JSON'}), 400
            else:
                account.set_cookies_dict(data['cookies'])
        
        # 设置认证数据
        if data.get('auth_data'):
            if isinstance(data['auth_data'], str):
                try:
                    auth_dict = json.loads(data['auth_data'])
                    account.set_auth_data_dict(auth_dict)
                except json.JSONDecodeError:
                    return jsonify({'error': '认证数据格式错误，必须是有效的JSON'}), 400
            else:
                account.set_auth_data_dict(data['auth_data'])
        
        db.session.add(account)
        db.session.commit()
        
        if request.is_json:
            return jsonify({'message': '账号创建成功', 'account_id': account.id})
        else:
            flash('账号创建成功', 'success')
            return redirect(url_for('accounts.view_account', account_id=account.id))
            
    except Exception as e:
        db.session.rollback()
        error_msg = f'创建账号失败: {str(e)}'
        if request.is_json:
            return jsonify({'error': error_msg}), 500
        else:
            flash(error_msg, 'error')
            return redirect(url_for('accounts.create_account'))

@accounts_bp.route('/<int:account_id>')
def view_account(account_id):
    """查看账号详情"""
    account = Account.query.get_or_404(account_id)
    
    # 获取相关任务
    monitor_tasks = account.monitor_tasks if account.account_type == 'monitor' else []
    upload_tasks = account.upload_tasks if account.account_type == 'upload' else []
    
    return render_template('accounts/detail.html', 
                         account=account,
                         monitor_tasks=monitor_tasks,
                         upload_tasks=upload_tasks)

@accounts_bp.route('/<int:account_id>/edit', methods=['GET', 'POST'])
def edit_account(account_id):
    """编辑账号"""
    account = Account.query.get_or_404(account_id)
    
    if request.method == 'GET':
        from config import Config
        return render_template('accounts/edit.html',
                             account=account,
                             monitor_platforms=Config.SUPPORTED_MONITOR_PLATFORMS,
                             upload_platforms=Config.SUPPORTED_UPLOAD_PLATFORMS)
    
    # POST 请求处理
    try:
        data = request.get_json() if request.is_json else request.form
        
        # 更新基本信息
        if 'username' in data:
            account.username = data['username']
        if 'display_name' in data:
            account.display_name = data['display_name']
        if 'user_id' in data:
            account.user_id = data['user_id']
        
        # 更新cookies
        if 'cookies' in data:
            if data['cookies']:
                if isinstance(data['cookies'], str):
                    try:
                        cookies_dict = json.loads(data['cookies'])
                        account.set_cookies_dict(cookies_dict)
                    except json.JSONDecodeError:
                        return jsonify({'error': 'cookies格式错误，必须是有效的JSON'}), 400
                else:
                    account.set_cookies_dict(data['cookies'])
            else:
                account.cookies = None
        
        # 更新认证数据
        if 'auth_data' in data:
            if data['auth_data']:
                if isinstance(data['auth_data'], str):
                    try:
                        auth_dict = json.loads(data['auth_data'])
                        account.set_auth_data_dict(auth_dict)
                    except json.JSONDecodeError:
                        return jsonify({'error': '认证数据格式错误，必须是有效的JSON'}), 400
                else:
                    account.set_auth_data_dict(data['auth_data'])
            else:
                account.auth_data = None
        
        # 更新状态
        if 'is_active' in data:
            account.is_active = bool(data['is_active'])
        
        account.updated_at = datetime.utcnow()
        db.session.commit()
        
        if request.is_json:
            return jsonify({'message': '账号更新成功'})
        else:
            flash('账号更新成功', 'success')
            return redirect(url_for('accounts.view_account', account_id=account.id))
            
    except Exception as e:
        db.session.rollback()
        error_msg = f'更新账号失败: {str(e)}'
        if request.is_json:
            return jsonify({'error': error_msg}), 500
        else:
            flash(error_msg, 'error')
            return redirect(url_for('accounts.edit_account', account_id=account_id))

@accounts_bp.route('/<int:account_id>/test', methods=['POST'])
def test_account(account_id):
    """测试账号连接"""
    account = Account.query.get_or_404(account_id)
    
    try:
        # 这里应该调用相应平台的测试函数
        # 暂时返回模拟结果
        
        # TODO: 实现实际的账号测试逻辑
        # 根据平台类型调用不同的测试函数
        
        # 模拟测试结果
        import random
        if random.choice([True, False]):
            account.update_status('active', None)
            return jsonify({'message': '账号测试成功', 'status': 'active'})
        else:
            error_msg = '账号认证失败，请检查cookies或认证信息'
            account.update_status('error', error_msg)
            return jsonify({'error': error_msg, 'status': 'error'}), 400
            
    except Exception as e:
        error_msg = f'测试账号失败: {str(e)}'
        account.update_status('error', error_msg)
        return jsonify({'error': error_msg}), 500

@accounts_bp.route('/<int:account_id>/delete', methods=['POST'])
def delete_account(account_id):
    """删除账号"""
    account = Account.query.get_or_404(account_id)
    
    try:
        # 检查是否有关联的任务
        if account.monitor_tasks or account.upload_tasks:
            return jsonify({'error': '无法删除有关联任务的账号，请先删除相关任务'}), 400
        
        account_name = f"{account.platform}:{account.username}"
        db.session.delete(account)
        db.session.commit()
        
        return jsonify({'message': f'账号 "{account_name}" 删除成功'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'删除账号失败: {str(e)}'}), 500

@accounts_bp.route('/api')
def api_list_accounts():
    """API - 获取账号列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    platform_filter = request.args.get('platform', '')
    type_filter = request.args.get('type', '')
    
    query = Account.query
    
    if platform_filter:
        query = query.filter_by(platform=platform_filter)
    if type_filter:
        query = query.filter_by(account_type=type_filter)
    
    accounts = query.order_by(Account.updated_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'accounts': [account.to_dict() for account in accounts.items],
        'total': accounts.total,
        'pages': accounts.pages,
        'current_page': accounts.page,
        'per_page': accounts.per_page
    })

@accounts_bp.route('/<int:account_id>/api')
def api_get_account(account_id):
    """API - 获取单个账号详情"""
    account = Account.query.get_or_404(account_id)
    
    account_data = account.to_dict()
    
    # 添加配置数据（不包含敏感信息）
    account_data['has_cookies'] = bool(account.cookies)
    account_data['has_auth_data'] = bool(account.auth_data)
    
    return jsonify(account_data)
