from flask import Blueprint, render_template, request, jsonify, send_file
from app.models import Video, Task, VideoProcessLog
from app import db
from datetime import datetime
import os

videos_bp = Blueprint('videos', __name__)

@videos_bp.route('/')
def list_videos():
    """视频列表页面"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    platform_filter = request.args.get('platform', '')
    status_filter = request.args.get('status', '')
    task_filter = request.args.get('task_id', '', type=str)
    
    query = Video.query
    
    # 应用过滤器
    if platform_filter:
        query = query.filter_by(original_platform=platform_filter)
    if status_filter:
        if status_filter == 'downloaded':
            query = query.filter_by(download_status='completed')
        elif status_filter == 'processed':
            query = query.filter_by(process_status='completed')
        elif status_filter == 'uploaded':
            query = query.filter_by(upload_status='completed')
        elif status_filter == 'failed':
            query = query.filter(
                (Video.download_status == 'failed') |
                (Video.process_status == 'failed') |
                (Video.upload_status == 'failed')
            )
    if task_filter:
        query = query.filter_by(task_id=int(task_filter))
    
    videos = query.order_by(Video.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # 获取所有平台和任务用于过滤器
    platforms = db.session.query(Video.original_platform).distinct().all()
    platforms = [p[0] for p in platforms]
    
    tasks = Task.query.filter_by(is_active=True).all()
    
    return render_template('videos/list.html',
                         videos=videos.items,
                         pagination=videos,
                         platforms=platforms,
                         tasks=tasks,
                         current_platform=platform_filter,
                         current_status=status_filter,
                         current_task=task_filter)

@videos_bp.route('/<int:video_id>')
def view_video(video_id):
    """查看视频详情"""
    video = Video.query.get_or_404(video_id)
    
    # 获取处理日志
    logs = video.process_logs.order_by(VideoProcessLog.created_at.desc()).all()
    
    # 检查文件是否存在
    file_exists = video.get_file_exists()
    
    return render_template('videos/detail.html', 
                         video=video, 
                         logs=logs,
                         file_exists=file_exists)

@videos_bp.route('/<int:video_id>/download')
def download_video(video_id):
    """下载视频文件"""
    video = Video.query.get_or_404(video_id)
    
    # 优先返回处理后的文件
    file_path = video.processed_path if video.processed_path and os.path.exists(video.processed_path) else video.local_path
    
    if not file_path or not os.path.exists(file_path):
        return jsonify({'error': '视频文件不存在'}), 404
    
    try:
        return send_file(
            file_path,
            as_attachment=True,
            download_name=video.file_name or f"video_{video.id}.mp4"
        )
    except Exception as e:
        return jsonify({'error': f'下载失败: {str(e)}'}), 500

@videos_bp.route('/<int:video_id>/reprocess', methods=['POST'])
def reprocess_video(video_id):
    """重新处理视频"""
    video = Video.query.get_or_404(video_id)
    
    try:
        # 重置处理状态
        video.process_status = 'pending'
        video.processed_at = None
        video.error_message = None
        video.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        # 添加处理日志
        video.add_process_log('process', 'pending', '视频已重新加入处理队列')
        
        # TODO: 这里应该触发实际的重新处理逻辑
        # 可以通过消息队列或者后台任务来处理
        
        return jsonify({'message': '视频已重新加入处理队列'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'重新处理失败: {str(e)}'}), 500

@videos_bp.route('/<int:video_id>/reupload', methods=['POST'])
def reupload_video(video_id):
    """重新上传视频"""
    video = Video.query.get_or_404(video_id)
    
    try:
        # 检查是否已处理
        if video.process_status != 'completed':
            return jsonify({'error': '视频尚未处理完成，无法上传'}), 400
        
        # 重置上传状态
        video.upload_status = 'pending'
        video.uploaded_at = None
        video.upload_video_id = None
        video.upload_url = None
        video.error_message = None
        video.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        # 添加处理日志
        video.add_process_log('upload', 'pending', '视频已重新加入上传队列')
        
        # TODO: 这里应该触发实际的重新上传逻辑
        
        return jsonify({'message': '视频已重新加入上传队列'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'重新上传失败: {str(e)}'}), 500

@videos_bp.route('/<int:video_id>/delete', methods=['POST'])
def delete_video(video_id):
    """删除视频"""
    video = Video.query.get_or_404(video_id)
    
    try:
        # 删除本地文件
        files_to_delete = []
        if video.local_path and os.path.exists(video.local_path):
            files_to_delete.append(video.local_path)
        if video.processed_path and os.path.exists(video.processed_path):
            files_to_delete.append(video.processed_path)
        
        # 删除数据库记录
        video_title = video.original_title or f"视频 {video.id}"
        db.session.delete(video)
        db.session.commit()
        
        # 删除文件
        for file_path in files_to_delete:
            try:
                os.remove(file_path)
            except OSError:
                pass  # 忽略文件删除错误
        
        return jsonify({'message': f'视频 "{video_title}" 删除成功'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'删除视频失败: {str(e)}'}), 500

@videos_bp.route('/batch-delete', methods=['POST'])
def batch_delete_videos():
    """批量删除视频"""
    try:
        data = request.get_json()
        video_ids = data.get('video_ids', [])
        
        if not video_ids:
            return jsonify({'error': '未选择要删除的视频'}), 400
        
        videos = Video.query.filter(Video.id.in_(video_ids)).all()
        
        if not videos:
            return jsonify({'error': '未找到要删除的视频'}), 404
        
        deleted_count = 0
        files_to_delete = []
        
        for video in videos:
            # 收集要删除的文件
            if video.local_path and os.path.exists(video.local_path):
                files_to_delete.append(video.local_path)
            if video.processed_path and os.path.exists(video.processed_path):
                files_to_delete.append(video.processed_path)
            
            # 删除数据库记录
            db.session.delete(video)
            deleted_count += 1
        
        db.session.commit()
        
        # 删除文件
        for file_path in files_to_delete:
            try:
                os.remove(file_path)
            except OSError:
                pass  # 忽略文件删除错误
        
        return jsonify({'message': f'成功删除 {deleted_count} 个视频'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'批量删除失败: {str(e)}'}), 500

@videos_bp.route('/api')
def api_list_videos():
    """API - 获取视频列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    platform_filter = request.args.get('platform', '')
    status_filter = request.args.get('status', '')
    task_filter = request.args.get('task_id', '', type=str)
    
    query = Video.query
    
    if platform_filter:
        query = query.filter_by(original_platform=platform_filter)
    if status_filter:
        if status_filter == 'downloaded':
            query = query.filter_by(download_status='completed')
        elif status_filter == 'processed':
            query = query.filter_by(process_status='completed')
        elif status_filter == 'uploaded':
            query = query.filter_by(upload_status='completed')
        elif status_filter == 'failed':
            query = query.filter(
                (Video.download_status == 'failed') |
                (Video.process_status == 'failed') |
                (Video.upload_status == 'failed')
            )
    if task_filter:
        query = query.filter_by(task_id=int(task_filter))
    
    videos = query.order_by(Video.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'videos': [video.to_dict() for video in videos.items],
        'total': videos.total,
        'pages': videos.pages,
        'current_page': videos.page,
        'per_page': videos.per_page
    })

@videos_bp.route('/<int:video_id>/api')
def api_get_video(video_id):
    """API - 获取单个视频详情"""
    video = Video.query.get_or_404(video_id)
    
    video_data = video.to_dict()
    
    # 添加额外信息
    video_data['file_exists'] = video.get_file_exists()
    video_data['processing_config'] = video.get_processing_config()
    video_data['upload_config'] = video.get_upload_config()
    
    # 添加任务信息
    if video.task:
        video_data['task'] = {
            'id': video.task.id,
            'name': video.task.name,
            'monitor_platform': video.task.monitor_platform,
            'upload_platform': video.task.upload_platform
        }
    
    return jsonify(video_data)
