from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from app.models import Task, Account, TaskLog
from app import db
from datetime import datetime, timedelta

tasks_bp = Blueprint('tasks', __name__)

@tasks_bp.route('/')
def list_tasks():
    """任务列表页面"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    status_filter = request.args.get('status', '')
    platform_filter = request.args.get('platform', '')
    
    query = Task.query
    
    # 应用过滤器
    if status_filter:
        query = query.filter_by(status=status_filter)
    if platform_filter:
        query = query.filter_by(monitor_platform=platform_filter)
    
    tasks = query.order_by(Task.updated_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # 获取所有平台用于过滤器
    platforms = db.session.query(Task.monitor_platform).distinct().all()
    platforms = [p[0] for p in platforms]
    
    return render_template('tasks/list.html',
                         tasks=tasks.items,
                         pagination=tasks,
                         platforms=platforms,
                         current_status=status_filter,
                         current_platform=platform_filter)

@tasks_bp.route('/create', methods=['GET', 'POST'])
def create_task():
    """创建任务"""
    if request.method == 'GET':
        # 获取可用的账号
        monitor_accounts = Account.get_monitor_accounts()
        upload_accounts = Account.get_upload_accounts()
        
        return render_template('tasks/create.html',
                             monitor_accounts=monitor_accounts,
                             upload_accounts=upload_accounts)
    
    # POST 请求处理
    try:
        data = request.get_json() if request.is_json else request.form
        
        # 验证必填字段
        required_fields = ['name', 'monitor_platform', 'monitor_account_id']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'缺少必填字段: {field}'}), 400

        # 验证监控账号存在性
        monitor_account = Account.query.get(data['monitor_account_id'])
        if not monitor_account or monitor_account.account_type != 'monitor':
            return jsonify({'error': '监控账号不存在或类型错误'}), 400

        # 验证上传账号（如果提供）
        upload_account = None
        if data.get('upload_account_id'):
            upload_account = Account.query.get(data['upload_account_id'])
            if not upload_account or upload_account.account_type != 'upload':
                return jsonify({'error': '上传账号不存在或类型错误'}), 400
        
        # 创建任务
        task = Task(
            name=data['name'],
            description=data.get('description', ''),
            monitor_platform=data['monitor_platform'],
            monitor_account_id=data['monitor_account_id'],
            monitor_interval=int(data.get('monitor_interval', 300)),
            max_videos=int(data.get('max_videos', 10)),
            is_active=bool(data.get('is_active', True)),
            auto_upload=bool(data.get('auto_upload', False)),
            upload_platform=upload_account.platform if upload_account else None,
            upload_account_id=data.get('upload_account_id')
        )

        # 设置监控配置
        if data.get('monitor_config'):
            task.set_monitor_config(data['monitor_config'])

        # 设置处理配置
        if data.get('process_config'):
            task.set_processing_config(data['process_config'])

        # 设置上传配置
        if data.get('upload_config'):
            task.set_upload_config(data['upload_config'])
        
        db.session.add(task)
        db.session.commit()
        
        # 添加创建日志
        task.add_log('INFO', f'任务创建成功: {task.name}')
        
        if request.is_json:
            return jsonify({'message': '任务创建成功', 'task_id': task.id})
        else:
            flash('任务创建成功', 'success')
            return redirect(url_for('tasks.view_task', task_id=task.id))
            
    except Exception as e:
        db.session.rollback()
        error_msg = f'创建任务失败: {str(e)}'
        if request.is_json:
            return jsonify({'error': error_msg}), 500
        else:
            flash(error_msg, 'error')
            return redirect(url_for('tasks.create_task'))

@tasks_bp.route('/<int:task_id>')
def view_task(task_id):
    """查看任务详情"""
    task = Task.query.get_or_404(task_id)
    
    # 获取任务日志
    logs = task.logs.order_by(TaskLog.created_at.desc()).limit(50).all()
    
    # 获取任务相关的视频
    videos = task.videos.order_by(task.videos.property.mapper.class_.created_at.desc()).limit(20).all()
    
    return render_template('tasks/detail.html', 
                         task=task, 
                         logs=logs, 
                         videos=videos)

@tasks_bp.route('/<int:task_id>/edit', methods=['GET', 'POST'])
def edit_task(task_id):
    """编辑任务"""
    task = Task.query.get_or_404(task_id)
    
    if request.method == 'GET':
        # 获取可用的账号
        monitor_accounts = Account.get_monitor_accounts()
        upload_accounts = Account.get_upload_accounts()
        
        return render_template('tasks/edit.html',
                             task=task,
                             monitor_accounts=monitor_accounts,
                             upload_accounts=upload_accounts)
    
    # POST 请求处理
    try:
        data = request.get_json() if request.is_json else request.form
        
        # 更新任务信息
        if 'name' in data:
            task.name = data['name']
        if 'description' in data:
            task.description = data['description']
        if 'monitor_interval' in data:
            task.monitor_interval = int(data['monitor_interval'])
        if 'enable_deduplication' in data:
            task.enable_deduplication = bool(data['enable_deduplication'])
        if 'enable_pseudo_original' in data:
            task.enable_pseudo_original = bool(data['enable_pseudo_original'])
        
        # 更新账号（如果提供）
        if 'monitor_account_id' in data:
            monitor_account = Account.query.get(data['monitor_account_id'])
            if monitor_account and monitor_account.account_type == 'monitor':
                task.monitor_account_id = data['monitor_account_id']
        
        if 'upload_account_id' in data:
            upload_account = Account.query.get(data['upload_account_id'])
            if upload_account and upload_account.account_type == 'upload':
                task.upload_account_id = data['upload_account_id']
        
        # 更新配置
        if 'processing_config' in data:
            task.set_processing_config(data['processing_config'])
        if 'upload_config' in data:
            task.set_upload_config(data['upload_config'])
        
        task.updated_at = datetime.utcnow()
        db.session.commit()
        
        # 添加更新日志
        task.add_log('INFO', f'任务配置已更新')
        
        if request.is_json:
            return jsonify({'message': '任务更新成功'})
        else:
            flash('任务更新成功', 'success')
            return redirect(url_for('tasks.view_task', task_id=task.id))
            
    except Exception as e:
        db.session.rollback()
        error_msg = f'更新任务失败: {str(e)}'
        if request.is_json:
            return jsonify({'error': error_msg}), 500
        else:
            flash(error_msg, 'error')
            return redirect(url_for('tasks.edit_task', task_id=task_id))

@tasks_bp.route('/<int:task_id>/control', methods=['POST'])
def control_task(task_id):
    """控制任务（启动/停止/暂停）"""
    task = Task.query.get_or_404(task_id)
    
    try:
        data = request.get_json() if request.is_json else request.form
        action = data.get('action')
        
        if action == 'start':
            if task.status in ['stopped', 'error']:
                task.status = 'running'
                task.next_run = datetime.utcnow() + timedelta(seconds=task.monitor_interval)
                task.add_log('INFO', '任务已启动')
                message = '任务启动成功'
            else:
                return jsonify({'error': '任务当前状态不允许启动'}), 400
                
        elif action == 'stop':
            if task.status in ['running', 'paused']:
                task.status = 'stopped'
                task.next_run = None
                task.add_log('INFO', '任务已停止')
                message = '任务停止成功'
            else:
                return jsonify({'error': '任务当前状态不允许停止'}), 400
                
        elif action == 'pause':
            if task.status == 'running':
                task.status = 'paused'
                task.add_log('INFO', '任务已暂停')
                message = '任务暂停成功'
            else:
                return jsonify({'error': '任务当前状态不允许暂停'}), 400
                
        elif action == 'resume':
            if task.status == 'paused':
                task.status = 'running'
                task.next_run = datetime.utcnow() + timedelta(seconds=task.monitor_interval)
                task.add_log('INFO', '任务已恢复')
                message = '任务恢复成功'
            else:
                return jsonify({'error': '任务当前状态不允许恢复'}), 400
        else:
            return jsonify({'error': '无效的操作'}), 400
        
        task.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({'message': message, 'status': task.status})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'操作失败: {str(e)}'}), 500

@tasks_bp.route('/<int:task_id>/delete', methods=['POST'])
def delete_task(task_id):
    """删除任务"""
    task = Task.query.get_or_404(task_id)
    
    try:
        # 检查任务是否正在运行
        if task.status == 'running':
            return jsonify({'error': '无法删除正在运行的任务，请先停止任务'}), 400
        
        task_name = task.name
        db.session.delete(task)
        db.session.commit()
        
        return jsonify({'message': f'任务 "{task_name}" 删除成功'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'删除任务失败: {str(e)}'}), 500

@tasks_bp.route('/api')
def api_list_tasks():
    """API - 获取任务列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    status_filter = request.args.get('status', '')
    platform_filter = request.args.get('platform', '')
    
    query = Task.query
    
    if status_filter:
        query = query.filter_by(status=status_filter)
    if platform_filter:
        query = query.filter_by(monitor_platform=platform_filter)
    
    tasks = query.order_by(Task.updated_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'tasks': [task.to_dict() for task in tasks.items],
        'total': tasks.total,
        'pages': tasks.pages,
        'current_page': tasks.page,
        'per_page': tasks.per_page
    })

@tasks_bp.route('/<int:task_id>/api')
def api_get_task(task_id):
    """API - 获取单个任务详情"""
    task = Task.query.get_or_404(task_id)
    
    task_data = task.to_dict()
    
    # 添加关联数据
    task_data['monitor_account'] = task.monitor_account.to_dict() if task.monitor_account else None
    task_data['upload_account'] = task.upload_account.to_dict() if task.upload_account else None
    task_data['processing_config'] = task.get_processing_config()
    task_data['upload_config'] = task.get_upload_config()
    
    return jsonify(task_data)
