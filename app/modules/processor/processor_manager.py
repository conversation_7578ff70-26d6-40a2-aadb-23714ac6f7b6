"""
视频处理管理器 - 管理所有视频处理操作
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import threading
import queue
import time
from app.models import Video, Task
from app import db
from .base_processor import DeduplicationProcessor, PseudoOriginalProcessor

class ProcessorManager:
    """视频处理管理器"""
    
    def __init__(self, max_workers: int = 3):
        """
        初始化处理管理器

        Args:
            max_workers: 最大工作线程数
        """
        self.logger = logging.getLogger('processor_manager')
        self.max_workers = max_workers
        self.processors = {}
        self.processing_queue = queue.Queue()
        self.workers = []
        self.running = False
        self.stats = {
            'processed': 0,
            'failed': 0,
            'skipped': 0
        }
        self.app = None  # Flask应用实例

        self._register_processors()
    
    def _register_processors(self):
        """注册所有处理器"""
        self.processors['deduplication'] = DeduplicationProcessor()
        self.processors['pseudo_original'] = PseudoOriginalProcessor()
        
        self.logger.info("视频处理器注册完成")
    
    def start(self, app=None):
        """启动处理管理器"""
        if self.running:
            self.logger.warning("处理管理器已在运行")
            return

        # 保存Flask应用实例
        if app:
            self.app = app

        self.running = True
        
        # 启动工作线程
        for i in range(self.max_workers):
            worker = threading.Thread(
                target=self._worker_thread,
                name=f"processor_worker_{i}",
                daemon=True
            )
            worker.start()
            self.workers.append(worker)
        
        # 启动队列监控线程
        monitor_thread = threading.Thread(
            target=self._queue_monitor,
            name="processor_queue_monitor",
            daemon=True
        )
        monitor_thread.start()
        
        self.logger.info(f"处理管理器启动，工作线程数: {self.max_workers}")
    
    def stop(self):
        """停止处理管理器"""
        if not self.running:
            return
        
        self.running = False
        
        # 等待所有工作线程结束
        for worker in self.workers:
            worker.join(timeout=30)
        
        self.workers.clear()
        self.logger.info("处理管理器已停止")
    
    def add_video_to_queue(self, video_id: int, priority: int = 0):
        """
        添加视频到处理队列
        
        Args:
            video_id: 视频ID
            priority: 优先级（数字越小优先级越高）
        """
        try:
            video = Video.query.get(video_id)
            if not video:
                self.logger.error(f"视频不存在: {video_id}")
                return
            
            if video.download_status != 'completed':
                self.logger.warning(f"视频未下载完成，跳过处理: {video_id}")
                return
            
            if video.process_status in ['completed', 'processing']:
                self.logger.warning(f"视频已处理或正在处理中: {video_id}")
                return
            
            # 添加到队列
            self.processing_queue.put((priority, video_id, datetime.utcnow()))
            
            # 更新状态
            video.update_status('process', 'pending')
            video.add_process_log('process', 'pending', '视频已加入处理队列')
            
            self.logger.info(f"视频已加入处理队列: {video_id}")
            
        except Exception as e:
            self.logger.error(f"添加视频到队列失败: {video_id}, 错误: {str(e)}")
    
    def process_video(self, video_id: int) -> Dict[str, Any]:
        """
        处理单个视频
        
        Args:
            video_id: 视频ID
            
        Returns:
            处理结果
        """
        try:
            video = Video.query.get(video_id)
            if not video:
                return {
                    'success': False,
                    'error': f'视频不存在: {video_id}'
                }
            
            if video.download_status != 'completed':
                return {
                    'success': False,
                    'error': '视频未下载完成'
                }
            
            # 获取任务配置
            task = video.task
            if not task:
                return {
                    'success': False,
                    'error': '关联任务不存在'
                }
            
            self.logger.info(f"开始处理视频: {video_id}")
            
            # 更新状态
            video.update_status('process', 'processing')
            video.add_process_log('process', 'started', '开始处理视频')
            
            results = []
            
            # 1. 去重检查
            if task.enable_deduplication:
                dedup_result = self._run_deduplication(video, task.get_processing_config())
                results.append(dedup_result)
                
                if not dedup_result['success']:
                    video.update_status('process', 'failed', dedup_result['error'])
                    return dedup_result
                
                # 如果是重复视频，跳过后续处理
                if dedup_result.get('duplicate'):
                    video.update_status('process', 'skipped', '重复视频，跳过处理')
                    self.stats['skipped'] += 1
                    return {
                        'success': True,
                        'skipped': True,
                        'reason': '重复视频',
                        'results': results
                    }
            
            # 2. 伪原创处理
            if task.enable_pseudo_original:
                pseudo_result = self._run_pseudo_original(video, task.get_processing_config())
                results.append(pseudo_result)
                
                if not pseudo_result['success']:
                    video.update_status('process', 'failed', pseudo_result['error'])
                    return pseudo_result
                
                # 更新处理后的文件路径
                if pseudo_result.get('output_path'):
                    video.processed_path = pseudo_result['output_path']
            
            # 处理完成
            video.update_status('process', 'completed')
            video.add_process_log('process', 'completed', '视频处理完成')
            
            self.stats['processed'] += 1
            self.logger.info(f"视频处理完成: {video_id}")
            
            return {
                'success': True,
                'results': results
            }
            
        except Exception as e:
            error_msg = f"处理视频异常: {str(e)}"
            self.logger.error(f"视频 {video_id} {error_msg}")
            
            try:
                video = Video.query.get(video_id)
                if video:
                    video.update_status('process', 'failed', error_msg)
                    video.add_process_log('process', 'failed', error_msg)
            except:
                pass
            
            self.stats['failed'] += 1
            
            return {
                'success': False,
                'error': error_msg
            }
    
    def _run_deduplication(self, video: Video, config: Dict[str, Any]) -> Dict[str, Any]:
        """运行去重检查"""
        processor = self.processors['deduplication']
        dedup_config = config.get('deduplication', {})
        
        result = processor.process_video(video, dedup_config)
        
        if result['success']:
            video.add_process_log('deduplication', 'completed', 
                                f"去重检查完成: {result['changes']}")
        else:
            video.add_process_log('deduplication', 'failed', 
                                f"去重检查失败: {result['error']}")
        
        return result
    
    def _run_pseudo_original(self, video: Video, config: Dict[str, Any]) -> Dict[str, Any]:
        """运行伪原创处理"""
        processor = self.processors['pseudo_original']
        pseudo_config = config.get('pseudo_original', {})
        
        result = processor.process_video(video, pseudo_config)
        
        if result['success']:
            video.add_process_log('pseudo_original', 'completed', 
                                f"伪原创处理完成: {result['changes']}")
        else:
            video.add_process_log('pseudo_original', 'failed', 
                                f"伪原创处理失败: {result['error']}")
        
        return result
    
    def _worker_thread(self):
        """工作线程"""
        thread_name = threading.current_thread().name
        self.logger.info(f"处理工作线程启动: {thread_name}")

        while self.running:
            try:
                # 从队列获取任务（超时1秒）
                try:
                    priority, video_id, queued_time = self.processing_queue.get(timeout=1)
                except queue.Empty:
                    continue

                # 在Flask应用上下文中处理视频
                if self.app:
                    with self.app.app_context():
                        self.process_video(video_id)
                else:
                    self.logger.warning("Flask应用上下文未设置，跳过视频处理")

                # 标记任务完成
                self.processing_queue.task_done()

            except Exception as e:
                self.logger.error(f"工作线程异常: {thread_name}, 错误: {str(e)}")
                time.sleep(1)

        self.logger.info(f"处理工作线程结束: {thread_name}")
    
    def _queue_monitor(self):
        """队列监控线程"""
        self.logger.info("队列监控线程启动")

        while self.running:
            try:
                if self.app:
                    with self.app.app_context():
                        # 检查待处理的视频
                        pending_videos = Video.query.filter_by(
                            download_status='completed',
                            process_status='pending'
                        ).limit(10).all()
                else:
                    pending_videos = []
                
                for video in pending_videos:
                    # 检查是否已在队列中
                    queue_items = []
                    temp_queue = queue.Queue()
                    
                    # 临时取出所有队列项目检查
                    while not self.processing_queue.empty():
                        try:
                            item = self.processing_queue.get_nowait()
                            queue_items.append(item)
                            temp_queue.put(item)
                        except queue.Empty:
                            break
                    
                    # 将项目放回队列
                    while not temp_queue.empty():
                        self.processing_queue.put(temp_queue.get())
                    
                    # 检查视频是否已在队列中
                    video_in_queue = any(item[1] == video.id for item in queue_items)
                    
                    if not video_in_queue:
                        self.add_video_to_queue(video.id)
                
                # 等待30秒后再次检查
                time.sleep(30)
                
            except Exception as e:
                self.logger.error(f"队列监控异常: {str(e)}")
                time.sleep(60)
        
        self.logger.info("队列监控线程结束")
    
    def get_queue_status(self) -> Dict[str, Any]:
        """获取队列状态"""
        return {
            'queue_size': self.processing_queue.qsize(),
            'running': self.running,
            'workers': len(self.workers),
            'stats': self.stats.copy()
        }
    
    def get_processing_videos(self) -> List[Dict[str, Any]]:
        """获取正在处理的视频列表"""
        processing_videos = Video.query.filter_by(process_status='processing').all()
        
        return [
            {
                'id': video.id,
                'title': video.original_title,
                'platform': video.original_platform,
                'task_id': video.task_id,
                'started_at': video.updated_at.isoformat() if video.updated_at else None
            }
            for video in processing_videos
        ]
    
    def retry_failed_videos(self, limit: int = 10):
        """重试失败的视频处理"""
        failed_videos = Video.query.filter_by(
            download_status='completed',
            process_status='failed'
        ).filter(Video.retry_count < 3).limit(limit).all()
        
        retried_count = 0
        for video in failed_videos:
            video.process_status = 'pending'
            video.error_message = None
            video.retry_count += 1
            video.updated_at = datetime.utcnow()
            
            video.add_process_log('process', 'retry', f'重试处理（第{video.retry_count}次）')
            retried_count += 1
        
        db.session.commit()
        
        self.logger.info(f"重试了 {retried_count} 个失败的视频处理")
        return retried_count

# 全局处理管理器实例
processor_manager = ProcessorManager()
