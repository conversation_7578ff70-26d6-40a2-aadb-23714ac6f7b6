"""
基础视频处理器 - 所有视频处理操作的基类
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
import logging
import os
import hashlib
from datetime import datetime
from app.models import Video
from app import db

class BaseProcessor(ABC):
    """基础视频处理器抽象类"""
    
    def __init__(self, processor_type: str):
        """
        初始化处理器
        
        Args:
            processor_type: 处理器类型
        """
        self.processor_type = processor_type
        self.logger = logging.getLogger(f'processor.{processor_type}')
    
    @abstractmethod
    def process_video(self, video: Video, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理视频
        
        Args:
            video: 视频对象
            config: 处理配置
            
        Returns:
            处理结果字典：
            - success: 是否成功
            - output_path: 输出文件路径
            - changes: 变更描述
            - error: 错误信息（如果失败）
        """
        pass
    
    def validate_video(self, video: Video) -> Dict[str, Any]:
        """
        验证视频文件
        
        Args:
            video: 视频对象
            
        Returns:
            验证结果字典：
            - valid: 是否有效
            - error: 错误信息（如果无效）
            - info: 视频信息
        """
        if not video.local_path or not os.path.exists(video.local_path):
            return {
                'valid': False,
                'error': '视频文件不存在'
            }
        
        try:
            # 检查文件大小
            file_size = os.path.getsize(video.local_path)
            if file_size == 0:
                return {
                    'valid': False,
                    'error': '视频文件为空'
                }
            
            # 检查文件格式（基于扩展名）
            _, ext = os.path.splitext(video.local_path)
            if ext.lower() not in ['.mp4', '.avi', '.mov', '.mkv', '.flv']:
                return {
                    'valid': False,
                    'error': f'不支持的视频格式: {ext}'
                }
            
            return {
                'valid': True,
                'info': {
                    'file_size': file_size,
                    'format': ext.lower()
                }
            }
            
        except Exception as e:
            return {
                'valid': False,
                'error': f'验证视频文件失败: {str(e)}'
            }
    
    def calculate_video_hash(self, video_path: str) -> str:
        """
        计算视频文件哈希值
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            文件的SHA256哈希值
        """
        hash_sha256 = hashlib.sha256()
        try:
            with open(video_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except Exception as e:
            self.logger.error(f"计算视频哈希失败: {video_path}, 错误: {str(e)}")
            return ""
    
    def get_output_path(self, video: Video, suffix: str = "_processed") -> str:
        """
        生成输出文件路径
        
        Args:
            video: 视频对象
            suffix: 文件名后缀
            
        Returns:
            输出文件路径
        """
        if not video.local_path:
            raise ValueError("视频本地路径为空")
        
        # 获取原文件信息
        dir_path = os.path.dirname(video.local_path)
        filename = os.path.basename(video.local_path)
        name, ext = os.path.splitext(filename)
        
        # 生成新文件名
        new_filename = f"{name}{suffix}{ext}"
        return os.path.join(dir_path, new_filename)
    
    def update_video_process_status(self, video: Video, status: str, 
                                  output_path: str = None, error_message: str = None):
        """
        更新视频处理状态
        
        Args:
            video: 视频对象
            status: 状态
            output_path: 输出文件路径
            error_message: 错误信息
        """
        video.update_status('process', status, error_message)
        
        if output_path and os.path.exists(output_path):
            video.processed_path = output_path
            video.processed_at = datetime.utcnow()
        
        db.session.commit()

class DeduplicationProcessor(BaseProcessor):
    """去重处理器"""
    
    def __init__(self):
        super().__init__('deduplication')
    
    def process_video(self, video: Video, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行去重检查
        
        Args:
            video: 视频对象
            config: 去重配置
            
        Returns:
            处理结果
        """
        try:
            self.logger.info(f"开始去重检查: {video.id}")
            
            # 验证视频文件
            validation = self.validate_video(video)
            if not validation['valid']:
                return {
                    'success': False,
                    'error': validation['error']
                }
            
            # 计算视频哈希
            if not video.content_hash:
                video.content_hash = self.calculate_video_hash(video.local_path)
                db.session.commit()
            
            # 检查是否有重复视频
            duplicate_video = Video.query.filter(
                Video.content_hash == video.content_hash,
                Video.id != video.id,
                Video.content_hash.isnot(None),
                Video.content_hash != ''
            ).first()
            
            if duplicate_video:
                # 标记为重复
                video.is_duplicate = True
                video.duplicate_of = duplicate_video.id
                video.update_status('process', 'skipped', '检测到重复视频')
                video.add_process_log('process', 'skipped', 
                                    f'检测到重复视频，原视频ID: {duplicate_video.id}')
                
                self.logger.info(f"检测到重复视频: {video.id} -> {duplicate_video.id}")
                
                return {
                    'success': True,
                    'duplicate': True,
                    'original_video_id': duplicate_video.id,
                    'changes': '标记为重复视频'
                }
            else:
                # 无重复，继续处理
                video.is_duplicate = False
                video.add_process_log('process', 'completed', '去重检查通过')
                
                self.logger.info(f"去重检查通过: {video.id}")
                
                return {
                    'success': True,
                    'duplicate': False,
                    'changes': '去重检查通过'
                }
                
        except Exception as e:
            error_msg = f"去重检查失败: {str(e)}"
            self.logger.error(f"视频 {video.id} {error_msg}")
            return {
                'success': False,
                'error': error_msg
            }

class PseudoOriginalProcessor(BaseProcessor):
    """伪原创处理器"""
    
    def __init__(self):
        super().__init__('pseudo_original')
    
    def process_video(self, video: Video, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行伪原创处理
        
        Args:
            video: 视频对象
            config: 伪原创配置
            
        Returns:
            处理结果
        """
        try:
            self.logger.info(f"开始伪原创处理: {video.id}")
            
            # 验证视频文件
            validation = self.validate_video(video)
            if not validation['valid']:
                return {
                    'success': False,
                    'error': validation['error']
                }
            
            # 获取配置参数
            operations = config.get('operations', [])
            if not operations:
                return {
                    'success': True,
                    'output_path': video.local_path,
                    'changes': '未配置伪原创操作'
                }
            
            # 生成输出路径
            output_path = self.get_output_path(video, "_pseudo")
            
            # 执行伪原创操作
            changes = []
            current_input = video.local_path
            
            for operation in operations:
                result = self._apply_operation(current_input, output_path, operation)
                if not result['success']:
                    return result
                
                changes.append(result['change'])
                current_input = result['output_path']
                output_path = result['output_path']
            
            # 处理标题和描述
            if config.get('modify_title', False):
                video.processed_title = self._modify_title(video.original_title)
                changes.append('修改标题')
            
            if config.get('modify_description', False):
                video.processed_description = self._modify_description(video.original_description)
                changes.append('修改描述')
            
            self.logger.info(f"伪原创处理完成: {video.id}")
            
            return {
                'success': True,
                'output_path': output_path,
                'changes': ', '.join(changes)
            }
            
        except Exception as e:
            error_msg = f"伪原创处理失败: {str(e)}"
            self.logger.error(f"视频 {video.id} {error_msg}")
            return {
                'success': False,
                'error': error_msg
            }
    
    def _apply_operation(self, input_path: str, output_path: str, 
                        operation: Dict[str, Any]) -> Dict[str, Any]:
        """
        应用单个伪原创操作
        
        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径
            operation: 操作配置
            
        Returns:
            操作结果
        """
        op_type = operation.get('type')
        
        if op_type == 'mirror':
            return self._mirror_video(input_path, output_path)
        elif op_type == 'crop':
            return self._crop_video(input_path, output_path, operation.get('params', {}))
        elif op_type == 'speed':
            return self._change_speed(input_path, output_path, operation.get('params', {}))
        elif op_type == 'watermark':
            return self._add_watermark(input_path, output_path, operation.get('params', {}))
        else:
            return {
                'success': False,
                'error': f'不支持的操作类型: {op_type}'
            }
    
    def _mirror_video(self, input_path: str, output_path: str) -> Dict[str, Any]:
        """镜像翻转视频"""
        # TODO: 实现视频镜像翻转
        # 这里应该使用 ffmpeg 或其他视频处理库
        return {
            'success': True,
            'output_path': output_path,
            'change': '镜像翻转'
        }
    
    def _crop_video(self, input_path: str, output_path: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """裁剪视频"""
        # TODO: 实现视频裁剪
        return {
            'success': True,
            'output_path': output_path,
            'change': '视频裁剪'
        }
    
    def _change_speed(self, input_path: str, output_path: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """改变播放速度"""
        # TODO: 实现速度调整
        return {
            'success': True,
            'output_path': output_path,
            'change': '调整播放速度'
        }
    
    def _add_watermark(self, input_path: str, output_path: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """添加水印"""
        # TODO: 实现水印添加
        return {
            'success': True,
            'output_path': output_path,
            'change': '添加水印'
        }
    
    def _modify_title(self, original_title: str) -> str:
        """修改标题"""
        if not original_title:
            return original_title
        
        # TODO: 实现智能标题修改
        # 可以使用同义词替换、句式调整等方法
        return f"【精选】{original_title}"
    
    def _modify_description(self, original_description: str) -> str:
        """修改描述"""
        if not original_description:
            return original_description
        
        # TODO: 实现智能描述修改
        return f"{original_description}\n\n#精选内容 #优质视频"
