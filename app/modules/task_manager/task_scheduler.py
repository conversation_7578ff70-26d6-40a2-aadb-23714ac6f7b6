"""
任务调度器 - 负责任务的调度和生命周期管理
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import threading
import time
from app.models import Task, TaskLog
from app import db
from app.modules.monitor.monitor_manager import monitor_manager
from app.modules.processor.processor_manager import processor_manager
from app.modules.account_manager.account_manager import account_manager

class TaskScheduler:
    """任务调度器"""

    def __init__(self):
        self.logger = logging.getLogger('task_scheduler')
        self.running = False
        self.scheduler_thread = None
        self.task_states = {}  # 任务状态缓存
        self.lock = threading.Lock()
        self.app = None  # Flask应用实例
    
    def start(self, app=None):
        """启动任务调度器"""
        if self.running:
            self.logger.warning("任务调度器已在运行")
            return

        # 保存Flask应用实例
        if app:
            self.app = app

        self.running = True

        # 启动调度线程
        self.scheduler_thread = threading.Thread(
            target=self._scheduler_loop,
            name="task_scheduler",
            daemon=True
        )
        self.scheduler_thread.start()

        # 启动相关服务
        processor_manager.start(app)
        account_manager.start_upload_service(app)

        # 重启所有活跃任务
        if app:
            with app.app_context():
                monitor_manager.restart_all_active_tasks()

        self.logger.info("任务调度器启动完成")
    
    def stop(self):
        """停止任务调度器"""
        if not self.running:
            return
        
        self.running = False
        
        # 停止相关服务
        monitor_manager.stop_all_tasks()
        processor_manager.stop()
        account_manager.stop_upload_service()
        
        # 等待调度线程结束
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=30)
        
        self.logger.info("任务调度器已停止")
    
    def _scheduler_loop(self):
        """调度器主循环"""
        self.logger.info("任务调度器主循环启动")

        while self.running:
            try:
                if self.app:
                    with self.app.app_context():
                        # 检查任务状态
                        self._check_task_status()

                        # 处理任务状态变更
                        self._handle_task_state_changes()

                        # 清理过期数据
                        self._cleanup_expired_data()
                else:
                    self.logger.warning("Flask应用上下文未设置，跳过此次检查")

                # 等待下次检查
                time.sleep(30)  # 每30秒检查一次

            except Exception as e:
                self.logger.error(f"调度器主循环异常: {str(e)}")
                time.sleep(60)  # 出错时等待更长时间

        self.logger.info("任务调度器主循环结束")
    
    def _check_task_status(self):
        """检查所有任务状态"""
        try:
            tasks = Task.query.filter_by(is_active=True).all()
            
            for task in tasks:
                with self.lock:
                    old_state = self.task_states.get(task.id, {})
                    new_state = {
                        'status': task.status,
                        'last_run': task.last_run,
                        'next_run': task.next_run,
                        'updated_at': task.updated_at
                    }
                    
                    # 检查状态是否变更
                    if old_state != new_state:
                        self.task_states[task.id] = new_state
                        self._handle_task_state_change(task, old_state, new_state)
                        
        except Exception as e:
            self.logger.error(f"检查任务状态异常: {str(e)}")
    
    def _handle_task_state_change(self, task: Task, old_state: Dict, new_state: Dict):
        """处理单个任务状态变更"""
        try:
            old_status = old_state.get('status')
            new_status = new_state.get('status')
            
            if old_status != new_status:
                self.logger.info(f"任务 {task.id} 状态变更: {old_status} -> {new_status}")
                
                if new_status == 'running':
                    self._start_task(task)
                elif new_status in ['paused', 'stopped']:
                    self._stop_task(task)
                elif new_status == 'error':
                    self._handle_task_error(task)
                    
        except Exception as e:
            self.logger.error(f"处理任务状态变更异常: {task.id}, 错误: {str(e)}")
    
    def _handle_task_state_changes(self):
        """处理任务状态变更"""
        try:
            # 检查需要启动的任务
            tasks_to_start = Task.query.filter_by(
                status='running',
                is_active=True
            ).all()
            
            for task in tasks_to_start:
                if not self._is_task_monitoring(task.id):
                    self._start_task(task)
            
            # 检查需要停止的任务
            running_task_ids = list(monitor_manager.running_tasks.keys())
            for task_id in running_task_ids:
                task = Task.query.get(task_id)
                if not task or task.status != 'running' or not task.is_active:
                    self._stop_task_by_id(task_id)
                    
        except Exception as e:
            self.logger.error(f"处理任务状态变更异常: {str(e)}")
    
    def _start_task(self, task: Task):
        """启动任务"""
        try:
            self.logger.info(f"启动任务: {task.id} - {task.name}")
            
            # 验证任务配置
            validation = self._validate_task_config(task)
            if not validation['valid']:
                task.update_status('error', validation['error'])
                task.add_log('ERROR', f'任务配置验证失败: {validation["error"]}')
                return
            
            # 启动监控
            monitor_manager.start_task_monitoring(task.id)
            
            # 更新任务状态
            task.last_run = datetime.utcnow()
            if not task.next_run:
                task.next_run = datetime.utcnow() + timedelta(seconds=task.monitor_interval)
            
            task.add_log('INFO', '任务启动成功')
            db.session.commit()
            
            self.logger.info(f"任务启动成功: {task.id}")
            
        except Exception as e:
            error_msg = f"启动任务失败: {str(e)}"
            self.logger.error(f"任务 {task.id} {error_msg}")
            
            task.update_status('error', error_msg)
            task.add_log('ERROR', error_msg)
    
    def _stop_task(self, task: Task):
        """停止任务"""
        try:
            self.logger.info(f"停止任务: {task.id} - {task.name}")
            
            # 停止监控
            monitor_manager.stop_task_monitoring(task.id)
            
            # 添加日志
            task.add_log('INFO', '任务已停止')
            
            self.logger.info(f"任务停止成功: {task.id}")
            
        except Exception as e:
            error_msg = f"停止任务失败: {str(e)}"
            self.logger.error(f"任务 {task.id} {error_msg}")
    
    def _stop_task_by_id(self, task_id: int):
        """根据ID停止任务"""
        try:
            monitor_manager.stop_task_monitoring(task_id)
            self.logger.info(f"任务停止成功: {task_id}")
        except Exception as e:
            self.logger.error(f"停止任务失败: {task_id}, 错误: {str(e)}")
    
    def _handle_task_error(self, task: Task):
        """处理任务错误"""
        try:
            self.logger.warning(f"任务出现错误: {task.id} - {task.error_message}")
            
            # 停止监控
            monitor_manager.stop_task_monitoring(task.id)
            
            # 检查是否需要自动重试
            if task.auto_retry and task.retry_count < 3:
                # 等待一段时间后重试
                retry_delay = min(300 * (task.retry_count + 1), 1800)  # 最多等待30分钟
                task.next_run = datetime.utcnow() + timedelta(seconds=retry_delay)
                task.retry_count += 1
                task.status = 'running'
                
                task.add_log('WARNING', f'任务将在 {retry_delay} 秒后重试（第{task.retry_count}次）')
                
                self.logger.info(f"任务将自动重试: {task.id}, 延迟: {retry_delay}秒")
            else:
                task.add_log('ERROR', '任务已停止，需要手动处理')
                
        except Exception as e:
            self.logger.error(f"处理任务错误异常: {task.id}, 错误: {str(e)}")
    
    def _validate_task_config(self, task: Task) -> Dict[str, Any]:
        """验证任务配置"""
        try:
            # 检查监控账号
            if not task.monitor_account:
                return {
                    'valid': False,
                    'error': '监控账号不存在'
                }
            
            if not task.monitor_account.is_active:
                return {
                    'valid': False,
                    'error': '监控账号未激活'
                }
            
            # 检查上传账号
            if not task.upload_account:
                return {
                    'valid': False,
                    'error': '上传账号不存在'
                }
            
            if not task.upload_account.is_active:
                return {
                    'valid': False,
                    'error': '上传账号未激活'
                }
            
            # 检查监控间隔
            if task.monitor_interval < 300:  # 最小5分钟
                return {
                    'valid': False,
                    'error': '监控间隔不能小于5分钟'
                }
            
            return {
                'valid': True
            }
            
        except Exception as e:
            return {
                'valid': False,
                'error': f'验证配置异常: {str(e)}'
            }
    
    def _is_task_monitoring(self, task_id: int) -> bool:
        """检查任务是否正在监控中"""
        return task_id in monitor_manager.running_tasks
    
    def _cleanup_expired_data(self):
        """清理过期数据"""
        try:
            # 清理过期的任务日志（保留30天）
            expire_date = datetime.utcnow() - timedelta(days=30)
            
            deleted_logs = TaskLog.query.filter(
                TaskLog.created_at < expire_date
            ).delete()
            
            if deleted_logs > 0:
                db.session.commit()
                self.logger.info(f"清理了 {deleted_logs} 条过期任务日志")
                
        except Exception as e:
            self.logger.error(f"清理过期数据异常: {str(e)}")
    
    def get_scheduler_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        running_tasks = monitor_manager.get_running_tasks()
        processor_status = processor_manager.get_queue_status()
        upload_status = account_manager.get_upload_queue_status()
        
        return {
            'running': self.running,
            'active_tasks': len(running_tasks),
            'running_tasks': running_tasks,
            'processor_queue': processor_status['queue_size'],
            'upload_queue': upload_status['queue_size'],
            'processor_stats': processor_status['stats'],
            'upload_stats': upload_status['stats']
        }
    
    def restart_task(self, task_id: int) -> Dict[str, Any]:
        """重启任务"""
        try:
            task = Task.query.get(task_id)
            if not task:
                return {
                    'success': False,
                    'error': '任务不存在'
                }
            
            # 先停止任务
            if self._is_task_monitoring(task_id):
                self._stop_task(task)
                time.sleep(2)  # 等待停止完成
            
            # 重置任务状态
            task.status = 'running'
            task.error_message = None
            task.retry_count = 0
            task.next_run = datetime.utcnow()
            
            # 启动任务
            self._start_task(task)
            
            return {
                'success': True,
                'message': '任务重启成功'
            }
            
        except Exception as e:
            error_msg = f"重启任务失败: {str(e)}"
            self.logger.error(f"任务 {task_id} {error_msg}")
            return {
                'success': False,
                'error': error_msg
            }
    
    def pause_task(self, task_id: int) -> Dict[str, Any]:
        """暂停任务"""
        try:
            task = Task.query.get(task_id)
            if not task:
                return {
                    'success': False,
                    'error': '任务不存在'
                }
            
            if task.status != 'running':
                return {
                    'success': False,
                    'error': '任务未在运行中'
                }
            
            # 停止监控
            self._stop_task(task)
            
            # 更新状态
            task.update_status('paused')
            task.add_log('INFO', '任务已暂停')
            
            return {
                'success': True,
                'message': '任务暂停成功'
            }
            
        except Exception as e:
            error_msg = f"暂停任务失败: {str(e)}"
            self.logger.error(f"任务 {task_id} {error_msg}")
            return {
                'success': False,
                'error': error_msg
            }
    
    def resume_task(self, task_id: int) -> Dict[str, Any]:
        """恢复任务"""
        try:
            task = Task.query.get(task_id)
            if not task:
                return {
                    'success': False,
                    'error': '任务不存在'
                }
            
            if task.status != 'paused':
                return {
                    'success': False,
                    'error': '任务未暂停'
                }
            
            # 更新状态
            task.update_status('running')
            task.next_run = datetime.utcnow()
            task.add_log('INFO', '任务已恢复')
            
            # 启动监控
            self._start_task(task)
            
            return {
                'success': True,
                'message': '任务恢复成功'
            }
            
        except Exception as e:
            error_msg = f"恢复任务失败: {str(e)}"
            self.logger.error(f"任务 {task_id} {error_msg}")
            return {
                'success': False,
                'error': error_msg
            }

# 全局任务调度器实例
task_scheduler = TaskScheduler()
