"""
任务执行器 - 负责任务的具体执行逻辑
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import threading
import time
from app.models import Task, Video, TaskLog
from app import db

class TaskExecutor:
    """任务执行器"""
    
    def __init__(self):
        self.logger = logging.getLogger('task_executor')
        self.executing_tasks = {}  # 正在执行的任务
        self.lock = threading.Lock()
    
    def execute_task_workflow(self, task_id: int) -> Dict[str, Any]:
        """
        执行完整的任务工作流
        
        Args:
            task_id: 任务ID
            
        Returns:
            执行结果
        """
        try:
            task = Task.query.get(task_id)
            if not task:
                return {
                    'success': False,
                    'error': '任务不存在'
                }
            
            self.logger.info(f"开始执行任务工作流: {task_id} - {task.name}")
            
            with self.lock:
                if task_id in self.executing_tasks:
                    return {
                        'success': False,
                        'error': '任务正在执行中'
                    }
                
                self.executing_tasks[task_id] = {
                    'start_time': datetime.utcnow(),
                    'status': 'running'
                }
            
            try:
                # 1. 监控阶段
                monitor_result = self._execute_monitor_phase(task)
                if not monitor_result['success']:
                    return monitor_result
                
                # 2. 处理阶段
                if monitor_result.get('new_videos', 0) > 0:
                    process_result = self._execute_process_phase(task)
                    if not process_result['success']:
                        return process_result
                    
                    # 3. 上传阶段
                    upload_result = self._execute_upload_phase(task)
                    if not upload_result['success']:
                        return upload_result
                
                # 更新任务统计
                self._update_task_statistics(task)
                
                return {
                    'success': True,
                    'monitor_result': monitor_result,
                    'process_result': process_result if 'process_result' in locals() else None,
                    'upload_result': upload_result if 'upload_result' in locals() else None
                }
                
            finally:
                with self.lock:
                    if task_id in self.executing_tasks:
                        del self.executing_tasks[task_id]
                        
        except Exception as e:
            error_msg = f"执行任务工作流异常: {str(e)}"
            self.logger.error(f"任务 {task_id} {error_msg}")
            
            with self.lock:
                if task_id in self.executing_tasks:
                    del self.executing_tasks[task_id]
            
            return {
                'success': False,
                'error': error_msg
            }
    
    def _execute_monitor_phase(self, task: Task) -> Dict[str, Any]:
        """执行监控阶段"""
        try:
            self.logger.info(f"执行监控阶段: {task.id}")
            
            # 获取监控账号
            monitor_account = task.monitor_account
            if not monitor_account or not monitor_account.is_active:
                return {
                    'success': False,
                    'error': '监控账号不可用'
                }
            
            # 这里应该调用具体的监控逻辑
            # 由于monitor_manager在task_scheduler中，这里模拟监控结果
            
            # 模拟监控结果
            new_videos = 0  # 实际应该从监控器获取
            downloaded = 0  # 实际应该从监控器获取
            
            task.add_log('INFO', f'监控完成 - 新视频: {new_videos}, 下载: {downloaded}')
            
            return {
                'success': True,
                'new_videos': new_videos,
                'downloaded': downloaded
            }
            
        except Exception as e:
            error_msg = f"监控阶段失败: {str(e)}"
            task.add_log('ERROR', error_msg)
            return {
                'success': False,
                'error': error_msg
            }
    
    def _execute_process_phase(self, task: Task) -> Dict[str, Any]:
        """执行处理阶段"""
        try:
            self.logger.info(f"执行处理阶段: {task.id}")
            
            # 获取待处理的视频
            pending_videos = Video.query.filter_by(
                task_id=task.id,
                download_status='completed',
                process_status='pending'
            ).limit(10).all()
            
            if not pending_videos:
                return {
                    'success': True,
                    'processed': 0,
                    'message': '没有待处理的视频'
                }
            
            processed_count = 0
            failed_count = 0
            
            for video in pending_videos:
                try:
                    # 这里应该调用processor_manager的处理逻辑
                    # 模拟处理结果
                    
                    # 去重检查
                    if task.enable_deduplication:
                        if self._check_duplicate(video):
                            video.update_status('process', 'skipped', '重复视频')
                            continue
                    
                    # 伪原创处理
                    if task.enable_pseudo_original:
                        self._apply_pseudo_original(video, task.get_processing_config())
                    
                    video.update_status('process', 'completed')
                    processed_count += 1
                    
                except Exception as e:
                    video.update_status('process', 'failed', str(e))
                    failed_count += 1
                    self.logger.error(f"处理视频失败: {video.id}, 错误: {str(e)}")
            
            task.add_log('INFO', f'处理完成 - 成功: {processed_count}, 失败: {failed_count}')
            
            return {
                'success': True,
                'processed': processed_count,
                'failed': failed_count
            }
            
        except Exception as e:
            error_msg = f"处理阶段失败: {str(e)}"
            task.add_log('ERROR', error_msg)
            return {
                'success': False,
                'error': error_msg
            }
    
    def _execute_upload_phase(self, task: Task) -> Dict[str, Any]:
        """执行上传阶段"""
        try:
            self.logger.info(f"执行上传阶段: {task.id}")
            
            # 获取待上传的视频
            ready_videos = Video.query.filter_by(
                task_id=task.id,
                process_status='completed',
                upload_status='pending'
            ).limit(5).all()  # 限制每次上传数量
            
            if not ready_videos:
                return {
                    'success': True,
                    'uploaded': 0,
                    'message': '没有待上传的视频'
                }
            
            # 检查上传账号
            upload_account = task.upload_account
            if not upload_account or not upload_account.is_active:
                return {
                    'success': False,
                    'error': '上传账号不可用'
                }
            
            uploaded_count = 0
            failed_count = 0
            
            for video in ready_videos:
                try:
                    # 这里应该调用account_manager的上传逻辑
                    # 模拟上传结果
                    
                    video.update_status('upload', 'uploading')
                    
                    # 模拟上传过程
                    time.sleep(1)  # 模拟上传时间
                    
                    # 模拟上传成功
                    video.upload_platform = task.upload_platform
                    video.upload_video_id = f"mock_video_{video.id}"
                    video.upload_url = f"https://{task.upload_platform}.com/video/{video.id}"
                    video.update_status('upload', 'completed')
                    
                    uploaded_count += 1
                    
                except Exception as e:
                    video.update_status('upload', 'failed', str(e))
                    failed_count += 1
                    self.logger.error(f"上传视频失败: {video.id}, 错误: {str(e)}")
            
            task.add_log('INFO', f'上传完成 - 成功: {uploaded_count}, 失败: {failed_count}')
            
            return {
                'success': True,
                'uploaded': uploaded_count,
                'failed': failed_count
            }
            
        except Exception as e:
            error_msg = f"上传阶段失败: {str(e)}"
            task.add_log('ERROR', error_msg)
            return {
                'success': False,
                'error': error_msg
            }
    
    def _check_duplicate(self, video: Video) -> bool:
        """检查视频是否重复"""
        if not video.content_hash:
            return False
        
        duplicate = Video.query.filter(
            Video.content_hash == video.content_hash,
            Video.id != video.id,
            Video.content_hash.isnot(None)
        ).first()
        
        if duplicate:
            video.is_duplicate = True
            video.duplicate_of = duplicate.id
            return True
        
        return False
    
    def _apply_pseudo_original(self, video: Video, config: Dict[str, Any]):
        """应用伪原创处理"""
        # 这里应该调用具体的伪原创处理逻辑
        # 模拟处理过程
        
        pseudo_config = config.get('pseudo_original', {})
        
        # 修改标题
        if pseudo_config.get('modify_title', False) and video.original_title:
            video.processed_title = f"【精选】{video.original_title}"
        
        # 修改描述
        if pseudo_config.get('modify_description', False) and video.original_description:
            video.processed_description = f"{video.original_description}\n\n#精选内容"
        
        # 这里应该还有视频文件的处理逻辑（镜像、裁剪等）
        # 由于需要实际的视频处理库，这里只做标记
        if pseudo_config.get('operations'):
            video.processed_path = video.local_path  # 模拟处理后的文件路径
    
    def _update_task_statistics(self, task: Task):
        """更新任务统计信息"""
        try:
            # 统计各状态的视频数量
            total_videos = Video.query.filter_by(task_id=task.id).count()
            downloaded_videos = Video.query.filter_by(
                task_id=task.id,
                download_status='completed'
            ).count()
            processed_videos = Video.query.filter_by(
                task_id=task.id,
                process_status='completed'
            ).count()
            uploaded_videos = Video.query.filter_by(
                task_id=task.id,
                upload_status='completed'
            ).count()
            
            # 更新任务统计
            task.total_monitored = total_videos
            task.total_downloaded = downloaded_videos
            task.total_uploaded = uploaded_videos
            
            # 更新最后运行时间
            task.last_run = datetime.utcnow()
            
            db.session.commit()
            
        except Exception as e:
            self.logger.error(f"更新任务统计失败: {task.id}, 错误: {str(e)}")
    
    def get_executing_tasks(self) -> Dict[int, Dict[str, Any]]:
        """获取正在执行的任务"""
        with self.lock:
            return {
                task_id: {
                    'start_time': info['start_time'].isoformat(),
                    'status': info['status'],
                    'duration': (datetime.utcnow() - info['start_time']).total_seconds()
                }
                for task_id, info in self.executing_tasks.items()
            }
    
    def is_task_executing(self, task_id: int) -> bool:
        """检查任务是否正在执行"""
        with self.lock:
            return task_id in self.executing_tasks
    
    def force_stop_task(self, task_id: int) -> Dict[str, Any]:
        """强制停止任务执行"""
        try:
            with self.lock:
                if task_id not in self.executing_tasks:
                    return {
                        'success': False,
                        'error': '任务未在执行中'
                    }
                
                del self.executing_tasks[task_id]
            
            # 更新任务状态
            task = Task.query.get(task_id)
            if task:
                task.add_log('WARNING', '任务执行被强制停止')
            
            return {
                'success': True,
                'message': '任务执行已强制停止'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'强制停止任务失败: {str(e)}'
            }

# 全局任务执行器实例
task_executor = TaskExecutor()
