"""
监控管理器 - 管理所有平台的监控器
"""

import logging
from typing import Dict, Optional, Any
from datetime import datetime, timedelta
import threading
import time
from app.models import Task, Account
from app import db
from .base_monitor import BaseMonitor

class MonitorManager:
    """监控管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger('monitor_manager')
        self.monitors: Dict[str, BaseMonitor] = {}
        self.running_tasks: Dict[int, threading.Thread] = {}
        self.stop_flags: Dict[int, threading.Event] = {}
        self._register_monitors()
    
    def _register_monitors(self):
        """注册所有平台的监控器"""
        # 这里将在后续添加具体平台的监控器
        # 示例：
        # from .xiaohongshu_monitor import XiaohongshuMonitor
        # from .douyin_monitor import DouyinMonitor
        # from .tiktok_monitor import TiktokMonitor
        # from .bilibili_monitor import BilibiliMonitor
        
        # self.register_monitor('xiaohongshu', XiaohongshuMonitor)
        # self.register_monitor('douyin', DouyinMonitor)
        # self.register_monitor('tiktok', TiktokMonitor)
        # self.register_monitor('bilibili', BilibiliMonitor)
        
        self.logger.info("监控器注册完成")
    
    def register_monitor(self, platform: str, monitor_class: type):
        """
        注册平台监控器

        Args:
            platform: 平台名称
            monitor_class: 监控器类
        """
        self.monitors[platform] = monitor_class
        self.logger.info(f"注册监控器: {platform}")

    def get_supported_platforms(self):
        """获取支持的平台列表"""
        return list(self.monitors.keys())
    
    def get_monitor(self, platform: str, account: Account) -> Optional[BaseMonitor]:
        """
        获取平台监控器实例
        
        Args:
            platform: 平台名称
            account: 账号对象
            
        Returns:
            监控器实例，如果平台不支持返回None
        """
        if platform not in self.monitors:
            self.logger.error(f"不支持的平台: {platform}")
            return None
        
        try:
            monitor_class = self.monitors[platform]
            return monitor_class(platform, account)
        except Exception as e:
            self.logger.error(f"创建监控器失败: {platform}, 错误: {str(e)}")
            return None
    
    def validate_account(self, account: Account) -> Dict[str, Any]:
        """
        验证账号有效性
        
        Args:
            account: 账号对象
            
        Returns:
            验证结果
        """
        monitor = self.get_monitor(account.platform, account)
        if not monitor:
            return {
                'valid': False,
                'error': f'不支持的平台: {account.platform}'
            }
        
        try:
            result = monitor.validate_account()
            
            # 更新账号状态
            if result.get('valid'):
                account.update_status('active', None)
            else:
                account.update_status('error', result.get('error', '验证失败'))
            
            return result
        except Exception as e:
            error_msg = f'验证账号异常: {str(e)}'
            account.update_status('error', error_msg)
            return {
                'valid': False,
                'error': error_msg
            }
    
    def start_task_monitoring(self, task_id: int):
        """
        启动任务监控
        
        Args:
            task_id: 任务ID
        """
        if task_id in self.running_tasks:
            self.logger.warning(f"任务 {task_id} 已在运行中")
            return
        
        task = Task.query.get(task_id)
        if not task:
            self.logger.error(f"任务不存在: {task_id}")
            return
        
        if not task.is_active:
            self.logger.warning(f"任务未激活: {task_id}")
            return
        
        # 创建停止标志
        stop_flag = threading.Event()
        self.stop_flags[task_id] = stop_flag
        
        # 创建监控线程
        monitor_thread = threading.Thread(
            target=self._monitor_task_worker,
            args=(task_id, stop_flag),
            name=f"monitor_task_{task_id}"
        )
        
        self.running_tasks[task_id] = monitor_thread
        monitor_thread.start()
        
        self.logger.info(f"启动任务监控: {task_id}")
    
    def stop_task_monitoring(self, task_id: int):
        """
        停止任务监控
        
        Args:
            task_id: 任务ID
        """
        if task_id not in self.running_tasks:
            self.logger.warning(f"任务 {task_id} 未在运行")
            return
        
        # 设置停止标志
        if task_id in self.stop_flags:
            self.stop_flags[task_id].set()
        
        # 等待线程结束
        thread = self.running_tasks[task_id]
        thread.join(timeout=30)  # 最多等待30秒
        
        # 清理
        del self.running_tasks[task_id]
        if task_id in self.stop_flags:
            del self.stop_flags[task_id]
        
        self.logger.info(f"停止任务监控: {task_id}")
    
    def _monitor_task_worker(self, task_id: int, stop_flag: threading.Event):
        """
        任务监控工作线程
        
        Args:
            task_id: 任务ID
            stop_flag: 停止标志
        """
        self.logger.info(f"任务监控线程启动: {task_id}")
        
        while not stop_flag.is_set():
            try:
                # 获取任务信息
                task = Task.query.get(task_id)
                if not task or not task.is_active or task.status != 'running':
                    self.logger.info(f"任务状态变更，停止监控: {task_id}")
                    break
                
                # 检查是否到了执行时间
                now = datetime.utcnow()
                if task.next_run and now < task.next_run:
                    # 等待到下次执行时间，但要检查停止标志
                    wait_seconds = min((task.next_run - now).total_seconds(), 60)
                    if stop_flag.wait(wait_seconds):
                        break
                    continue
                
                # 执行监控
                self._execute_task_monitoring(task)
                
                # 更新下次执行时间
                task.next_run = now + timedelta(seconds=task.monitor_interval)
                task.last_run = now
                task.total_monitored += 1
                db.session.commit()
                
                # 等待监控间隔，但要检查停止标志
                if stop_flag.wait(min(task.monitor_interval, 60)):
                    break
                    
            except Exception as e:
                self.logger.error(f"任务监控异常: {task_id}, 错误: {str(e)}")
                
                # 更新任务状态
                try:
                    task = Task.query.get(task_id)
                    if task:
                        task.update_status('error', str(e))
                        task.add_log('ERROR', f'监控异常: {str(e)}')
                except:
                    pass
                
                # 等待一段时间后重试
                if stop_flag.wait(300):  # 等待5分钟
                    break
        
        self.logger.info(f"任务监控线程结束: {task_id}")
    
    def _execute_task_monitoring(self, task: Task):
        """
        执行任务监控
        
        Args:
            task: 任务对象
        """
        try:
            self.logger.info(f"执行任务监控: {task.id} - {task.name}")
            
            # 获取监控账号
            monitor_account = task.monitor_account
            if not monitor_account:
                raise Exception("监控账号不存在")
            
            if not monitor_account.is_active:
                raise Exception("监控账号未激活")
            
            # 获取监控器
            monitor = self.get_monitor(task.monitor_platform, monitor_account)
            if not monitor:
                raise Exception(f"无法创建监控器: {task.monitor_platform}")
            
            # 执行监控
            result = monitor.monitor_user(
                user_id=monitor_account.user_id,
                task_id=task.id,
                limit=20  # 每次最多获取20个视频
            )
            
            if result.get('success'):
                # 更新统计信息
                task.total_downloaded += result.get('downloaded', 0)
                
                # 添加日志
                message = (f"监控完成 - 新视频: {result.get('new_videos', 0)}, "
                          f"下载成功: {result.get('downloaded', 0)}, "
                          f"下载失败: {result.get('failed', 0)}")
                task.add_log('INFO', message)
                
                self.logger.info(f"任务 {task.id} 监控成功: {message}")
            else:
                error_msg = result.get('error', '监控失败')
                task.add_log('ERROR', f'监控失败: {error_msg}')
                self.logger.error(f"任务 {task.id} 监控失败: {error_msg}")
                
        except Exception as e:
            error_msg = f"执行监控异常: {str(e)}"
            task.add_log('ERROR', error_msg)
            self.logger.error(f"任务 {task.id} {error_msg}")
            raise
    
    def get_running_tasks(self) -> Dict[int, Dict[str, Any]]:
        """
        获取正在运行的任务信息
        
        Returns:
            运行中的任务信息字典
        """
        running_info = {}
        
        for task_id, thread in self.running_tasks.items():
            task = Task.query.get(task_id)
            if task:
                running_info[task_id] = {
                    'task_name': task.name,
                    'platform': task.monitor_platform,
                    'status': task.status,
                    'thread_alive': thread.is_alive(),
                    'last_run': task.last_run.isoformat() if task.last_run else None,
                    'next_run': task.next_run.isoformat() if task.next_run else None
                }
        
        return running_info
    
    def restart_all_active_tasks(self):
        """重启所有活跃任务的监控"""
        active_tasks = Task.query.filter_by(status='running', is_active=True).all()
        
        for task in active_tasks:
            if task.id not in self.running_tasks:
                self.start_task_monitoring(task.id)
        
        self.logger.info(f"重启了 {len(active_tasks)} 个活跃任务的监控")
    
    def stop_all_tasks(self):
        """停止所有任务监控"""
        task_ids = list(self.running_tasks.keys())
        
        for task_id in task_ids:
            self.stop_task_monitoring(task_id)
        
        self.logger.info(f"停止了 {len(task_ids)} 个任务的监控")

# 全局监控管理器实例
monitor_manager = MonitorManager()
