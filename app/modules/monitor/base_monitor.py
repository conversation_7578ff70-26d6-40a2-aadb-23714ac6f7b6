"""
基础监控类 - 所有平台监控器的基类
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging
import hashlib
import os
from app.models import Video, Account
from app import db

class BaseMonitor(ABC):
    """基础监控器抽象类"""
    
    def __init__(self, platform: str, account: Account):
        """
        初始化监控器
        
        Args:
            platform: 平台名称
            account: 监控账号对象
        """
        self.platform = platform
        self.account = account
        self.logger = logging.getLogger(f'monitor.{platform}')
        
    @abstractmethod
    def get_user_videos(self, user_id: str, limit: int = 20) -> List[Dict[str, Any]]:
        """
        获取用户的视频列表
        
        Args:
            user_id: 用户ID
            limit: 获取数量限制
            
        Returns:
            视频信息列表，每个视频包含以下字段：
            - video_id: 视频ID
            - title: 标题
            - description: 描述
            - author: 作者
            - author_id: 作者ID
            - url: 视频URL
            - publish_time: 发布时间
            - duration: 时长（秒）
            - view_count: 播放量
            - like_count: 点赞数
            - comment_count: 评论数
        """
        pass
    
    @abstractmethod
    def download_video(self, video_info: Dict[str, Any], save_path: str) -> Dict[str, Any]:
        """
        下载视频
        
        Args:
            video_info: 视频信息字典
            save_path: 保存路径
            
        Returns:
            下载结果字典：
            - success: 是否成功
            - file_path: 文件路径
            - file_size: 文件大小
            - duration: 视频时长
            - resolution: 分辨率
            - format: 视频格式
            - error: 错误信息（如果失败）
        """
        pass
    
    @abstractmethod
    def validate_account(self) -> Dict[str, Any]:
        """
        验证账号有效性
        
        Returns:
            验证结果字典：
            - valid: 是否有效
            - error: 错误信息（如果无效）
            - user_info: 用户信息（如果有效）
        """
        pass
    
    def check_video_exists(self, video_id: str, task_id: int) -> Optional[Video]:
        """
        检查视频是否已存在
        
        Args:
            video_id: 视频ID
            task_id: 任务ID
            
        Returns:
            存在的视频对象，不存在返回None
        """
        return Video.query.filter_by(
            original_video_id=video_id,
            original_platform=self.platform,
            task_id=task_id
        ).first()
    
    def create_video_record(self, video_info: Dict[str, Any], task_id: int) -> Video:
        """
        创建视频记录
        
        Args:
            video_info: 视频信息
            task_id: 任务ID
            
        Returns:
            创建的视频对象
        """
        video = Video(
            task_id=task_id,
            original_platform=self.platform,
            original_video_id=video_info['video_id'],
            original_url=video_info.get('url', ''),
            original_title=video_info.get('title', ''),
            original_description=video_info.get('description', ''),
            original_author=video_info.get('author', ''),
            original_author_id=video_info.get('author_id', ''),
            original_publish_time=video_info.get('publish_time'),
            download_status='pending'
        )
        
        db.session.add(video)
        db.session.commit()
        
        return video
    
    def update_video_download_info(self, video: Video, download_result: Dict[str, Any]):
        """
        更新视频下载信息
        
        Args:
            video: 视频对象
            download_result: 下载结果
        """
        if download_result.get('success'):
            video.local_path = download_result.get('file_path')
            video.file_name = os.path.basename(download_result.get('file_path', ''))
            video.file_size = download_result.get('file_size')
            video.duration = download_result.get('duration')
            video.resolution = download_result.get('resolution')
            video.format = download_result.get('format')
            video.update_status('download', 'completed')
            
            # 计算内容哈希
            if video.local_path and os.path.exists(video.local_path):
                video.content_hash = self.calculate_file_hash(video.local_path)
            
            video.add_process_log('download', 'completed', '视频下载成功')
            self.logger.info(f"视频下载成功: {video.original_video_id}")
        else:
            error_msg = download_result.get('error', '未知错误')
            video.update_status('download', 'failed', error_msg)
            video.add_process_log('download', 'failed', f'视频下载失败: {error_msg}')
            self.logger.error(f"视频下载失败: {video.original_video_id}, 错误: {error_msg}")
    
    def calculate_file_hash(self, file_path: str) -> str:
        """
        计算文件哈希值
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件的SHA256哈希值
        """
        hash_sha256 = hashlib.sha256()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except Exception as e:
            self.logger.error(f"计算文件哈希失败: {file_path}, 错误: {str(e)}")
            return ""
    
    def get_download_path(self, video_info: Dict[str, Any], base_path: str) -> str:
        """
        生成下载路径
        
        Args:
            video_info: 视频信息
            base_path: 基础路径
            
        Returns:
            完整的下载路径
        """
        # 创建平台目录
        platform_dir = os.path.join(base_path, self.platform)
        os.makedirs(platform_dir, exist_ok=True)
        
        # 创建作者目录
        author = video_info.get('author', 'unknown').replace('/', '_').replace('\\', '_')
        author_dir = os.path.join(platform_dir, author)
        os.makedirs(author_dir, exist_ok=True)
        
        # 生成文件名
        video_id = video_info['video_id']
        title = video_info.get('title', video_id)[:50]  # 限制标题长度
        # 清理文件名中的非法字符
        safe_title = "".join(c for c in title if c.isalnum() or c in (' ', '-', '_')).rstrip()
        
        filename = f"{video_id}_{safe_title}.mp4"
        return os.path.join(author_dir, filename)
    
    def monitor_user(self, user_id: str, task_id: int, limit: int = 20) -> Dict[str, Any]:
        """
        监控用户的视频更新
        
        Args:
            user_id: 用户ID
            task_id: 任务ID
            limit: 获取数量限制
            
        Returns:
            监控结果字典：
            - success: 是否成功
            - new_videos: 新视频数量
            - total_videos: 总视频数量
            - downloaded: 下载成功数量
            - failed: 下载失败数量
            - error: 错误信息（如果失败）
        """
        try:
            self.logger.info(f"开始监控用户: {user_id}")
            
            # 获取用户视频列表
            videos = self.get_user_videos(user_id, limit)
            
            if not videos:
                return {
                    'success': True,
                    'new_videos': 0,
                    'total_videos': 0,
                    'downloaded': 0,
                    'failed': 0,
                    'message': '未获取到新视频'
                }
            
            new_videos = 0
            downloaded = 0
            failed = 0
            
            from config import Config
            base_download_path = Config.UPLOAD_FOLDER
            
            for video_info in videos:
                # 检查视频是否已存在
                existing_video = self.check_video_exists(video_info['video_id'], task_id)
                if existing_video:
                    continue
                
                # 创建视频记录
                video = self.create_video_record(video_info, task_id)
                new_videos += 1
                
                # 下载视频
                try:
                    download_path = self.get_download_path(video_info, base_download_path)
                    video.update_status('download', 'downloading')
                    video.add_process_log('download', 'started', '开始下载视频')
                    
                    download_result = self.download_video(video_info, download_path)
                    self.update_video_download_info(video, download_result)
                    
                    if download_result.get('success'):
                        downloaded += 1
                    else:
                        failed += 1
                        
                except Exception as e:
                    error_msg = f"下载视频异常: {str(e)}"
                    video.update_status('download', 'failed', error_msg)
                    video.add_process_log('download', 'failed', error_msg)
                    failed += 1
                    self.logger.error(error_msg)
            
            result = {
                'success': True,
                'new_videos': new_videos,
                'total_videos': len(videos),
                'downloaded': downloaded,
                'failed': failed
            }
            
            self.logger.info(f"监控完成: {result}")
            return result
            
        except Exception as e:
            error_msg = f"监控用户失败: {str(e)}"
            self.logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'new_videos': 0,
                'total_videos': 0,
                'downloaded': 0,
                'failed': 0
            }
