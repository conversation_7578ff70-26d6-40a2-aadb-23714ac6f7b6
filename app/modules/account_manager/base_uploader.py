"""
基础上传器 - 所有平台上传器的基类
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
import logging
import os
from datetime import datetime
from app.models import Video, Account
from app import db

class BaseUploader(ABC):
    """基础上传器抽象类"""
    
    def __init__(self, platform: str, account: Account):
        """
        初始化上传器
        
        Args:
            platform: 平台名称
            account: 上传账号对象
        """
        self.platform = platform
        self.account = account
        self.logger = logging.getLogger(f'uploader.{platform}')
    
    @abstractmethod
    def upload_video(self, video: Video, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        上传视频
        
        Args:
            video: 视频对象
            config: 上传配置
            
        Returns:
            上传结果字典：
            - success: 是否成功
            - video_id: 平台视频ID
            - url: 视频URL
            - error: 错误信息（如果失败）
        """
        pass
    
    @abstractmethod
    def validate_account(self) -> Dict[str, Any]:
        """
        验证账号有效性
        
        Returns:
            验证结果字典：
            - valid: 是否有效
            - error: 错误信息（如果无效）
            - user_info: 用户信息（如果有效）
        """
        pass
    
    @abstractmethod
    def get_upload_limits(self) -> Dict[str, Any]:
        """
        获取上传限制信息
        
        Returns:
            限制信息字典：
            - max_file_size: 最大文件大小（字节）
            - max_duration: 最大时长（秒）
            - supported_formats: 支持的格式列表
            - daily_limit: 每日上传限制
        """
        pass
    
    def validate_video_for_upload(self, video: Video) -> Dict[str, Any]:
        """
        验证视频是否可以上传
        
        Args:
            video: 视频对象
            
        Returns:
            验证结果字典：
            - valid: 是否有效
            - error: 错误信息（如果无效）
        """
        # 检查视频文件是否存在
        video_path = video.processed_path if video.processed_path else video.local_path
        
        if not video_path or not os.path.exists(video_path):
            return {
                'valid': False,
                'error': '视频文件不存在'
            }
        
        # 检查文件大小
        file_size = os.path.getsize(video_path)
        if file_size == 0:
            return {
                'valid': False,
                'error': '视频文件为空'
            }
        
        # 获取平台限制
        limits = self.get_upload_limits()
        
        # 检查文件大小限制
        if limits.get('max_file_size') and file_size > limits['max_file_size']:
            return {
                'valid': False,
                'error': f'文件大小超出限制: {file_size} > {limits["max_file_size"]}'
            }
        
        # 检查时长限制
        if limits.get('max_duration') and video.duration and video.duration > limits['max_duration']:
            return {
                'valid': False,
                'error': f'视频时长超出限制: {video.duration} > {limits["max_duration"]}'
            }
        
        # 检查格式支持
        if limits.get('supported_formats'):
            _, ext = os.path.splitext(video_path)
            if ext.lower() not in limits['supported_formats']:
                return {
                    'valid': False,
                    'error': f'不支持的视频格式: {ext}'
                }
        
        return {
            'valid': True,
            'file_path': video_path,
            'file_size': file_size
        }
    
    def prepare_upload_data(self, video: Video, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        准备上传数据
        
        Args:
            video: 视频对象
            config: 上传配置
            
        Returns:
            上传数据字典
        """
        # 获取标题
        title = video.processed_title if video.processed_title else video.original_title
        if not title:
            title = f"视频_{video.id}"
        
        # 应用标题配置
        title_config = config.get('title', {})
        if title_config.get('prefix'):
            title = f"{title_config['prefix']}{title}"
        if title_config.get('suffix'):
            title = f"{title}{title_config['suffix']}"
        if title_config.get('max_length'):
            title = title[:title_config['max_length']]
        
        # 获取描述
        description = video.processed_description if video.processed_description else video.original_description
        if not description:
            description = ""
        
        # 应用描述配置
        desc_config = config.get('description', {})
        if desc_config.get('prefix'):
            description = f"{desc_config['prefix']}\n{description}"
        if desc_config.get('suffix'):
            description = f"{description}\n{desc_config['suffix']}"
        if desc_config.get('max_length'):
            description = description[:desc_config['max_length']]
        
        # 获取标签
        tags = config.get('tags', [])
        if isinstance(tags, str):
            tags = [tag.strip() for tag in tags.split(',') if tag.strip()]
        
        return {
            'title': title,
            'description': description,
            'tags': tags,
            'privacy': config.get('privacy', 'public'),
            'category': config.get('category', ''),
            'thumbnail': config.get('thumbnail', ''),
            'schedule_time': config.get('schedule_time', None)
        }
    
    def update_video_upload_info(self, video: Video, upload_result: Dict[str, Any]):
        """
        更新视频上传信息
        
        Args:
            video: 视频对象
            upload_result: 上传结果
        """
        if upload_result.get('success'):
            video.upload_platform = self.platform
            video.upload_video_id = upload_result.get('video_id', '')
            video.upload_url = upload_result.get('url', '')
            video.update_status('upload', 'completed')
            
            video.add_process_log('upload', 'completed', 
                                f'视频上传成功到 {self.platform}')
            
            self.logger.info(f"视频上传成功: {video.id} -> {upload_result.get('video_id')}")
        else:
            error_msg = upload_result.get('error', '未知错误')
            video.update_status('upload', 'failed', error_msg)
            video.add_process_log('upload', 'failed', 
                                f'视频上传失败: {error_msg}')
            
            self.logger.error(f"视频上传失败: {video.id}, 错误: {error_msg}")
    
    def get_video_file_path(self, video: Video) -> Optional[str]:
        """
        获取视频文件路径
        
        Args:
            video: 视频对象
            
        Returns:
            视频文件路径，优先返回处理后的文件
        """
        if video.processed_path and os.path.exists(video.processed_path):
            return video.processed_path
        elif video.local_path and os.path.exists(video.local_path):
            return video.local_path
        else:
            return None
    
    def check_daily_upload_limit(self) -> Dict[str, Any]:
        """
        检查每日上传限制
        
        Returns:
            限制检查结果：
            - within_limit: 是否在限制内
            - uploaded_today: 今日已上传数量
            - limit: 每日限制数量
            - remaining: 剩余可上传数量
        """
        limits = self.get_upload_limits()
        daily_limit = limits.get('daily_limit')
        
        if not daily_limit:
            return {
                'within_limit': True,
                'uploaded_today': 0,
                'limit': None,
                'remaining': None
            }
        
        # 统计今日上传数量
        today = datetime.utcnow().date()
        uploaded_today = Video.query.filter(
            Video.upload_platform == self.platform,
            Video.upload_status == 'completed',
            db.func.date(Video.uploaded_at) == today
        ).count()
        
        remaining = max(0, daily_limit - uploaded_today)
        
        return {
            'within_limit': uploaded_today < daily_limit,
            'uploaded_today': uploaded_today,
            'limit': daily_limit,
            'remaining': remaining
        }
    
    def upload_video_with_validation(self, video: Video, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        带验证的视频上传
        
        Args:
            video: 视频对象
            config: 上传配置
            
        Returns:
            上传结果
        """
        try:
            self.logger.info(f"开始上传视频: {video.id} 到 {self.platform}")
            
            # 验证账号
            account_validation = self.validate_account()
            if not account_validation.get('valid'):
                return {
                    'success': False,
                    'error': f'账号验证失败: {account_validation.get("error")}'
                }
            
            # 验证视频
            video_validation = self.validate_video_for_upload(video)
            if not video_validation.get('valid'):
                return {
                    'success': False,
                    'error': f'视频验证失败: {video_validation.get("error")}'
                }
            
            # 检查每日上传限制
            limit_check = self.check_daily_upload_limit()
            if not limit_check.get('within_limit'):
                return {
                    'success': False,
                    'error': f'超出每日上传限制: {limit_check.get("uploaded_today")}/{limit_check.get("limit")}'
                }
            
            # 更新状态
            video.update_status('upload', 'uploading')
            video.add_process_log('upload', 'started', f'开始上传到 {self.platform}')
            
            # 执行上传
            upload_result = self.upload_video(video, config)
            
            # 更新上传信息
            self.update_video_upload_info(video, upload_result)
            
            return upload_result
            
        except Exception as e:
            error_msg = f"上传视频异常: {str(e)}"
            self.logger.error(f"视频 {video.id} {error_msg}")
            
            video.update_status('upload', 'failed', error_msg)
            video.add_process_log('upload', 'failed', error_msg)
            
            return {
                'success': False,
                'error': error_msg
            }
