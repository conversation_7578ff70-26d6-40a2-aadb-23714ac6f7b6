"""
账号管理器 - 管理所有平台的账号和上传器
"""

import logging
from typing import Dict, Optional, Any, List
from datetime import datetime, timedelta
import threading
import queue
import time
from app.models import Account, Video, Task
from app import db
from .base_uploader import BaseUploader

class AccountManager:
    """账号管理器"""
    
    def __init__(self, max_upload_workers: int = 2):
        """
        初始化账号管理器

        Args:
            max_upload_workers: 最大上传工作线程数
        """
        self.logger = logging.getLogger('account_manager')
        self.max_upload_workers = max_upload_workers
        self.uploaders: Dict[str, type] = {}
        self.upload_queue = queue.Queue()
        self.upload_workers = []
        self.running = False
        self.upload_stats = {
            'uploaded': 0,
            'failed': 0,
            'skipped': 0
        }
        self.app = None  # Flask应用实例

        self._register_uploaders()
    
    def _register_uploaders(self):
        """注册所有平台的上传器"""
        # 这里将在后续添加具体平台的上传器
        # 示例：
        # from .xiaohongshu_uploader import XiaohongshuUploader
        # from .tiktok_uploader import TiktokUploader
        # from .youtube_uploader import YoutubeUploader
        # from .bilibili_uploader import BilibiliUploader
        
        # self.register_uploader('xiaohongshu', XiaohongshuUploader)
        # self.register_uploader('tiktok', TiktokUploader)
        # self.register_uploader('youtube', YoutubeUploader)
        # self.register_uploader('bilibili', BilibiliUploader)
        
        self.logger.info("上传器注册完成")
    
    def register_uploader(self, platform: str, uploader_class: type):
        """
        注册平台上传器

        Args:
            platform: 平台名称
            uploader_class: 上传器类
        """
        self.uploaders[platform] = uploader_class
        self.logger.info(f"注册上传器: {platform}")

    def get_supported_platforms(self):
        """获取支持的平台列表"""
        return list(self.uploaders.keys())
    
    def get_uploader(self, platform: str, account: Account) -> Optional[BaseUploader]:
        """
        获取平台上传器实例
        
        Args:
            platform: 平台名称
            account: 账号对象
            
        Returns:
            上传器实例，如果平台不支持返回None
        """
        if platform not in self.uploaders:
            self.logger.error(f"不支持的上传平台: {platform}")
            return None
        
        try:
            uploader_class = self.uploaders[platform]
            return uploader_class(platform, account)
        except Exception as e:
            self.logger.error(f"创建上传器失败: {platform}, 错误: {str(e)}")
            return None
    
    def validate_account(self, account: Account) -> Dict[str, Any]:
        """
        验证账号有效性
        
        Args:
            account: 账号对象
            
        Returns:
            验证结果
        """
        uploader = self.get_uploader(account.platform, account)
        if not uploader:
            return {
                'valid': False,
                'error': f'不支持的平台: {account.platform}'
            }
        
        try:
            result = uploader.validate_account()
            
            # 更新账号状态
            if result.get('valid'):
                account.update_status('active', None)
            else:
                account.update_status('error', result.get('error', '验证失败'))
            
            return result
        except Exception as e:
            error_msg = f'验证账号异常: {str(e)}'
            account.update_status('error', error_msg)
            return {
                'valid': False,
                'error': error_msg
            }
    
    def get_account_upload_limits(self, account: Account) -> Dict[str, Any]:
        """
        获取账号上传限制
        
        Args:
            account: 账号对象
            
        Returns:
            限制信息
        """
        uploader = self.get_uploader(account.platform, account)
        if not uploader:
            return {
                'error': f'不支持的平台: {account.platform}'
            }
        
        try:
            return uploader.get_upload_limits()
        except Exception as e:
            return {
                'error': f'获取限制信息失败: {str(e)}'
            }
    
    def start_upload_service(self, app=None):
        """启动上传服务"""
        if self.running:
            self.logger.warning("上传服务已在运行")
            return

        # 保存Flask应用实例
        if app:
            self.app = app

        self.running = True
        
        # 启动上传工作线程
        for i in range(self.max_upload_workers):
            worker = threading.Thread(
                target=self._upload_worker_thread,
                name=f"upload_worker_{i}",
                daemon=True
            )
            worker.start()
            self.upload_workers.append(worker)
        
        # 启动队列监控线程
        monitor_thread = threading.Thread(
            target=self._upload_queue_monitor,
            name="upload_queue_monitor",
            daemon=True
        )
        monitor_thread.start()
        
        self.logger.info(f"上传服务启动，工作线程数: {self.max_upload_workers}")
    
    def stop_upload_service(self):
        """停止上传服务"""
        if not self.running:
            return
        
        self.running = False
        
        # 等待所有工作线程结束
        for worker in self.upload_workers:
            worker.join(timeout=30)
        
        self.upload_workers.clear()
        self.logger.info("上传服务已停止")
    
    def add_video_to_upload_queue(self, video_id: int, priority: int = 0):
        """
        添加视频到上传队列
        
        Args:
            video_id: 视频ID
            priority: 优先级（数字越小优先级越高）
        """
        try:
            video = Video.query.get(video_id)
            if not video:
                self.logger.error(f"视频不存在: {video_id}")
                return
            
            if video.process_status != 'completed':
                self.logger.warning(f"视频未处理完成，跳过上传: {video_id}")
                return
            
            if video.upload_status in ['completed', 'uploading']:
                self.logger.warning(f"视频已上传或正在上传中: {video_id}")
                return
            
            # 添加到队列
            self.upload_queue.put((priority, video_id, datetime.utcnow()))
            
            # 更新状态
            video.update_status('upload', 'pending')
            video.add_process_log('upload', 'pending', '视频已加入上传队列')
            
            self.logger.info(f"视频已加入上传队列: {video_id}")
            
        except Exception as e:
            self.logger.error(f"添加视频到上传队列失败: {video_id}, 错误: {str(e)}")
    
    def upload_video(self, video_id: int) -> Dict[str, Any]:
        """
        上传单个视频
        
        Args:
            video_id: 视频ID
            
        Returns:
            上传结果
        """
        try:
            video = Video.query.get(video_id)
            if not video:
                return {
                    'success': False,
                    'error': f'视频不存在: {video_id}'
                }
            
            if video.process_status != 'completed':
                return {
                    'success': False,
                    'error': '视频未处理完成'
                }
            
            # 获取任务和上传账号
            task = video.task
            if not task:
                return {
                    'success': False,
                    'error': '关联任务不存在'
                }
            
            upload_account = task.upload_account
            if not upload_account:
                return {
                    'success': False,
                    'error': '上传账号不存在'
                }
            
            if not upload_account.is_active:
                return {
                    'success': False,
                    'error': '上传账号未激活'
                }
            
            self.logger.info(f"开始上传视频: {video_id} 到 {task.upload_platform}")
            
            # 获取上传器
            uploader = self.get_uploader(task.upload_platform, upload_account)
            if not uploader:
                return {
                    'success': False,
                    'error': f'无法创建上传器: {task.upload_platform}'
                }
            
            # 获取上传配置
            upload_config = task.get_upload_config()
            
            # 执行上传
            result = uploader.upload_video_with_validation(video, upload_config)
            
            if result['success']:
                # 更新任务统计
                task.total_uploaded += 1
                db.session.commit()
                
                self.upload_stats['uploaded'] += 1
                self.logger.info(f"视频上传成功: {video_id}")
            else:
                self.upload_stats['failed'] += 1
                self.logger.error(f"视频上传失败: {video_id}, 错误: {result.get('error')}")
            
            return result
            
        except Exception as e:
            error_msg = f"上传视频异常: {str(e)}"
            self.logger.error(f"视频 {video_id} {error_msg}")
            
            try:
                video = Video.query.get(video_id)
                if video:
                    video.update_status('upload', 'failed', error_msg)
                    video.add_process_log('upload', 'failed', error_msg)
            except:
                pass
            
            self.upload_stats['failed'] += 1
            
            return {
                'success': False,
                'error': error_msg
            }
    
    def _upload_worker_thread(self):
        """上传工作线程"""
        thread_name = threading.current_thread().name
        self.logger.info(f"上传工作线程启动: {thread_name}")

        while self.running:
            try:
                # 从队列获取任务（超时1秒）
                try:
                    priority, video_id, queued_time = self.upload_queue.get(timeout=1)
                except queue.Empty:
                    continue

                # 在Flask应用上下文中上传视频
                if self.app:
                    with self.app.app_context():
                        self.upload_video(video_id)
                else:
                    self.logger.warning("Flask应用上下文未设置，跳过视频上传")

                # 标记任务完成
                self.upload_queue.task_done()

                # 上传间隔（避免频繁上传）
                time.sleep(5)

            except Exception as e:
                self.logger.error(f"上传工作线程异常: {thread_name}, 错误: {str(e)}")
                time.sleep(10)

        self.logger.info(f"上传工作线程结束: {thread_name}")
    
    def _upload_queue_monitor(self):
        """上传队列监控线程"""
        self.logger.info("上传队列监控线程启动")

        while self.running:
            try:
                if self.app:
                    with self.app.app_context():
                        # 检查待上传的视频
                        pending_videos = Video.query.filter_by(
                            process_status='completed',
                            upload_status='pending'
                        ).limit(5).all()
                else:
                    pending_videos = []
                
                for video in pending_videos:
                    # 检查是否已在队列中
                    queue_items = []
                    temp_queue = queue.Queue()
                    
                    # 临时取出所有队列项目检查
                    while not self.upload_queue.empty():
                        try:
                            item = self.upload_queue.get_nowait()
                            queue_items.append(item)
                            temp_queue.put(item)
                        except queue.Empty:
                            break
                    
                    # 将项目放回队列
                    while not temp_queue.empty():
                        self.upload_queue.put(temp_queue.get())
                    
                    # 检查视频是否已在队列中
                    video_in_queue = any(item[1] == video.id for item in queue_items)
                    
                    if not video_in_queue:
                        self.add_video_to_upload_queue(video.id)
                
                # 等待60秒后再次检查
                time.sleep(60)
                
            except Exception as e:
                self.logger.error(f"上传队列监控异常: {str(e)}")
                time.sleep(120)
        
        self.logger.info("上传队列监控线程结束")
    
    def get_upload_queue_status(self) -> Dict[str, Any]:
        """获取上传队列状态"""
        return {
            'queue_size': self.upload_queue.qsize(),
            'running': self.running,
            'workers': len(self.upload_workers),
            'stats': self.upload_stats.copy()
        }
    
    def get_uploading_videos(self) -> List[Dict[str, Any]]:
        """获取正在上传的视频列表"""
        uploading_videos = Video.query.filter_by(upload_status='uploading').all()
        
        return [
            {
                'id': video.id,
                'title': video.original_title,
                'platform': video.upload_platform,
                'task_id': video.task_id,
                'started_at': video.updated_at.isoformat() if video.updated_at else None
            }
            for video in uploading_videos
        ]
    
    def retry_failed_uploads(self, limit: int = 5):
        """重试失败的上传"""
        failed_videos = Video.query.filter_by(
            process_status='completed',
            upload_status='failed'
        ).filter(Video.retry_count < 3).limit(limit).all()
        
        retried_count = 0
        for video in failed_videos:
            video.upload_status = 'pending'
            video.error_message = None
            video.retry_count += 1
            video.updated_at = datetime.utcnow()
            
            video.add_process_log('upload', 'retry', f'重试上传（第{video.retry_count}次）')
            retried_count += 1
        
        db.session.commit()
        
        self.logger.info(f"重试了 {retried_count} 个失败的上传")
        return retried_count
    
    def get_account_statistics(self, account_id: int, days: int = 30) -> Dict[str, Any]:
        """
        获取账号统计信息
        
        Args:
            account_id: 账号ID
            days: 统计天数
            
        Returns:
            统计信息
        """
        account = Account.query.get(account_id)
        if not account:
            return {'error': '账号不存在'}
        
        # 计算时间范围
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # 统计上传数据
        if account.account_type == 'upload':
            # 通过任务关联统计上传视频
            upload_count = Video.query.join(Task).filter(
                Task.upload_account_id == account_id,
                Video.upload_status == 'completed',
                Video.uploaded_at >= start_date
            ).count()
            
            failed_count = Video.query.join(Task).filter(
                Task.upload_account_id == account_id,
                Video.upload_status == 'failed',
                Video.updated_at >= start_date
            ).count()
            
            return {
                'account_type': 'upload',
                'uploaded_videos': upload_count,
                'failed_uploads': failed_count,
                'success_rate': upload_count / (upload_count + failed_count) * 100 if (upload_count + failed_count) > 0 else 0,
                'period_days': days
            }
        
        elif account.account_type == 'monitor':
            # 通过任务关联统计监控数据
            monitored_count = Video.query.join(Task).filter(
                Task.monitor_account_id == account_id,
                Video.created_at >= start_date
            ).count()
            
            downloaded_count = Video.query.join(Task).filter(
                Task.monitor_account_id == account_id,
                Video.download_status == 'completed',
                Video.downloaded_at >= start_date
            ).count()
            
            return {
                'account_type': 'monitor',
                'monitored_videos': monitored_count,
                'downloaded_videos': downloaded_count,
                'download_rate': downloaded_count / monitored_count * 100 if monitored_count > 0 else 0,
                'period_days': days
            }
        
        return {'error': '未知账号类型'}

# 全局账号管理器实例
account_manager = AccountManager()
