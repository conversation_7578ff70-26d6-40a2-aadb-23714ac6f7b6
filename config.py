import os
from datetime import timedelta

class Config:
    """基础配置类"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here'
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///database.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # 文件上传配置
    UPLOAD_FOLDER = 'downloads'
    MAX_CONTENT_LENGTH = 500 * 1024 * 1024  # 500MB
    
    # 支持的平台配置
    SUPPORTED_MONITOR_PLATFORMS = ['xiaohongshu', 'douyin', 'tiktok', 'bilibili']
    SUPPORTED_UPLOAD_PLATFORMS = ['xiaohongshu', 'tiktok', 'youtube', 'bilibili']
    
    # 监控配置
    DEFAULT_MONITOR_INTERVAL = 300  # 5分钟
    MIN_MONITOR_INTERVAL = 60      # 最小1分钟
    MAX_MONITOR_INTERVAL = 86400   # 最大24小时
    
    # 视频处理配置
    VIDEO_FORMATS = ['mp4', 'avi', 'mov', 'mkv', 'flv']
    MAX_VIDEO_SIZE = 100 * 1024 * 1024  # 100MB
    
    # 任务配置
    MAX_CONCURRENT_TASKS = 5
    TASK_TIMEOUT = 3600  # 1小时
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_FILE = 'logs/app.log'
    
    @staticmethod
    def init_app(app):
        """初始化应用配置"""
        # 创建必要的目录
        os.makedirs(Config.UPLOAD_FOLDER, exist_ok=True)
        os.makedirs('logs', exist_ok=True)
        os.makedirs('upload_cookies', exist_ok=True)

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    
class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    
class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///test.db'

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
