#!/usr/bin/env python3
"""
视频监控管理系统 - 主启动文件
"""

import os
import sys
import logging
from datetime import datetime
from app import create_app, db
from app.models import User, Account, Task, Video
from app.modules.task_manager.task_scheduler import task_scheduler

def setup_logging():
    """设置日志配置"""
    # 创建logs目录
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s %(name)s %(levelname)s: %(message)s',
        handlers=[
            logging.FileHandler(f'logs/app_{datetime.now().strftime("%Y%m%d")}.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def init_database(app):
    """初始化数据库"""
    with app.app_context():
        try:
            # 创建所有表
            db.create_all()
            
            # 检查是否需要创建默认用户
            if User.query.count() == 0:
                default_user = User(
                    username='admin',
                    email='<EMAIL>'
                )
                default_user.set_password('admin123')
                db.session.add(default_user)
                db.session.commit()
                print("创建默认管理员用户: admin / admin123")
            
            print("数据库初始化完成")
            
        except Exception as e:
            print(f"数据库初始化失败: {str(e)}")
            sys.exit(1)

def print_system_info():
    """打印系统信息"""
    print("=" * 60)
    print("视频监控管理系统")
    print("=" * 60)
    print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print("=" * 60)

def print_startup_info(app):
    """打印启动信息"""
    with app.app_context():
        # 统计数据
        total_accounts = Account.query.count()
        active_accounts = Account.query.filter_by(is_active=True).count()
        total_tasks = Task.query.count()
        active_tasks = Task.query.filter_by(is_active=True).count()
        total_videos = Video.query.count()
        
        print("\n系统状态:")
        print(f"  账号总数: {total_accounts} (活跃: {active_accounts})")
        print(f"  任务总数: {total_tasks} (活跃: {active_tasks})")
        print(f"  视频总数: {total_videos}")
        print()

def start_background_services():
    """启动后台服务"""
    try:
        # 启动任务调度器
        task_scheduler.start()
        print("任务调度器启动成功")
        
    except Exception as e:
        print(f"启动后台服务失败: {str(e)}")
        return False
    
    return True

def main():
    """主函数"""
    # 设置日志
    setup_logging()
    
    # 打印系统信息
    print_system_info()
    
    # 创建Flask应用
    app = create_app()
    
    # 初始化数据库
    init_database(app)
    
    # 打印启动信息
    print_startup_info(app)
    
    # 启动后台服务
    if not start_background_services():
        print("后台服务启动失败，退出程序")
        sys.exit(1)
    
    # 获取配置
    host = os.environ.get('HOST', '0.0.0.0')
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('FLASK_ENV') == 'development'
    
    print(f"\n服务器启动信息:")
    print(f"  访问地址: http://{host}:{port}")
    print(f"  调试模式: {'开启' if debug else '关闭'}")
    print(f"  环境变量: {os.environ.get('FLASK_ENV', 'production')}")
    print("\n按 Ctrl+C 停止服务器")
    print("=" * 60)
    
    try:
        # 启动Flask应用
        app.run(
            host=host,
            port=port,
            debug=debug,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n\n正在停止服务器...")
        
        # 停止后台服务
        try:
            task_scheduler.stop()
            print("任务调度器已停止")
        except Exception as e:
            print(f"停止后台服务时出错: {str(e)}")
        
        print("服务器已停止")
    except Exception as e:
        print(f"服务器运行出错: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
