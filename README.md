# 视频监控管理系统

一个基于Flask的多平台视频监控、下载、处理和自动上传系统。

## 功能特性

### 核心功能
- **多平台监控**: 支持TikTok、抖音、B站、小红书等平台的视频监控
- **自动下载**: 监控到新视频后自动下载到本地
- **视频处理**: 支持去重检测和伪原创处理（镜像、裁剪、变速等）
- **自动上传**: 处理完成后自动上传到目标平台
- **任务管理**: 灵活的任务配置和调度系统

### 系统特性
- **Web管理界面**: 直观的Web界面进行系统管理
- **模块化架构**: 可扩展的模块化设计
- **多线程处理**: 支持并发处理多个任务
- **状态监控**: 实时监控系统运行状态
- **日志记录**: 详细的操作日志和错误追踪

## 系统架构

```
app/
├── __init__.py              # Flask应用工厂
├── config.py               # 配置文件
├── models/                 # 数据模型
│   ├── __init__.py
│   ├── user.py            # 用户模型
│   ├── account.py         # 账号模型
│   ├── task.py            # 任务模型
│   └── video.py           # 视频模型
├── routes/                 # 路由控制器
│   ├── __init__.py
│   ├── main.py            # 主页面路由
│   ├── tasks.py           # 任务管理路由
│   ├── accounts.py        # 账号管理路由
│   └── videos.py          # 视频管理路由
├── modules/               # 核心功能模块
│   ├── monitor/           # 监控模块
│   │   ├── __init__.py
│   │   ├── base_monitor.py      # 监控基类
│   │   └── monitor_manager.py   # 监控管理器
│   ├── processor/         # 处理模块
│   │   ├── __init__.py
│   │   ├── base_processor.py    # 处理基类
│   │   └── processor_manager.py # 处理管理器
│   ├── account_manager/   # 账号管理模块
│   │   ├── __init__.py
│   │   ├── base_uploader.py     # 上传基类
│   │   └── account_manager.py   # 账号管理器
│   └── task_manager/      # 任务管理模块
│       ├── __init__.py
│       ├── task_scheduler.py    # 任务调度器
│       └── task_executor.py     # 任务执行器
└── templates/             # Web模板
    ├── base.html          # 基础模板
    └── dashboard.html     # 仪表板模板
```

## 安装部署

### 环境要求
- Python 3.8+
- SQLite 3 (或其他支持的数据库)

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd videoManage
```

2. **创建虚拟环境**
```bash
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
# 或
.venv\Scripts\activate     # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **配置环境变量**
```bash
# 创建 .env 文件
cp .env.example .env

# 编辑配置
FLASK_ENV=development
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///app.db
```

5. **初始化数据库**
```bash
python run.py
```

6. **启动服务**
```bash
python run.py
```

访问 http://localhost:5000 进入系统管理界面。

默认管理员账号：
- 用户名: admin
- 密码: admin123

## 使用说明

### 1. 账号管理
- 添加监控账号：用于监控目标用户的视频
- 添加上传账号：用于上传处理后的视频
- 配置账号的cookies和认证信息

### 2. 任务配置
- 创建监控任务，关联监控账号和上传账号
- 配置监控间隔、处理参数、上传设置
- 启动/停止/暂停任务

### 3. 视频处理
- 自动去重检测，避免重复处理
- 伪原创处理：镜像翻转、视频裁剪、速度调整等
- 标题和描述的智能修改

### 4. 系统监控
- 实时查看任务运行状态
- 监控队列状态和处理进度
- 查看系统日志和错误信息

## 扩展开发

### 添加新平台支持

1. **创建监控器**
```python
# app/modules/monitor/platform_monitor.py
from .base_monitor import BaseMonitor

class PlatformMonitor(BaseMonitor):
    def get_user_videos(self, user_id, limit=20):
        # 实现平台特定的视频获取逻辑
        pass
    
    def download_video(self, video_info):
        # 实现平台特定的视频下载逻辑
        pass
```

2. **创建上传器**
```python
# app/modules/account_manager/platform_uploader.py
from .base_uploader import BaseUploader

class PlatformUploader(BaseUploader):
    def upload_video(self, video, config):
        # 实现平台特定的视频上传逻辑
        pass
```

3. **注册到管理器**
```python
# 在相应的管理器中注册新的平台支持
monitor_manager.register_monitor('platform', PlatformMonitor)
account_manager.register_uploader('platform', PlatformUploader)
```

### 自定义处理器

```python
# app/modules/processor/custom_processor.py
from .base_processor import BaseProcessor

class CustomProcessor(BaseProcessor):
    def process_video(self, video, config):
        # 实现自定义的视频处理逻辑
        pass
```

## 配置说明

### 主要配置项

```python
# config.py
class Config:
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = 'sqlite:///app.db'
    
    # 文件上传配置
    UPLOAD_FOLDER = 'uploads'
    MAX_CONTENT_LENGTH = 500 * 1024 * 1024  # 500MB
    
    # 支持的平台
    SUPPORTED_PLATFORMS = {
        'monitor': ['tiktok', 'douyin', 'bilibili', 'xiaohongshu'],
        'upload': ['tiktok', 'youtube', 'bilibili', 'xiaohongshu']
    }
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置和权限
   - 确保数据库文件路径正确

2. **任务无法启动**
   - 检查账号配置是否正确
   - 验证账号的cookies是否有效

3. **视频下载失败**
   - 检查网络连接
   - 验证目标平台的访问权限

4. **上传失败**
   - 检查上传账号的认证信息
   - 验证视频格式和大小限制

### 日志查看

系统日志保存在 `logs/` 目录下：
- `app_YYYYMMDD.log`: 应用日志
- 各模块会生成对应的日志文件

## 开发计划

- [ ] 添加更多平台支持
- [ ] 实现更多视频处理功能
- [ ] 添加用户权限管理
- [ ] 支持分布式部署
- [ ] 添加API接口
- [ ] 移动端管理界面

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue
- 发送邮件至: [<EMAIL>]
